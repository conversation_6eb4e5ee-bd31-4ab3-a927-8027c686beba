#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=1440:00:00
#PBS -P gs_spms_psengupta
#PBS -N kitaev-unified
#PBS -j oe

# ==================== 任务选择配置 ====================
# 设置要执行的任务类型，可以选择一个或多个：
# TRAIN     - 基础训练任务
# FINETUNE  - 链式微调任务  
# ANALYZE   - 分析任务
# 
# 示例：
# TASKS="TRAIN"                    # 只执行训练
# TASKS="TRAIN FINETUNE"           # 先训练后微调
# TASKS="ANALYZE"                  # 只执行分析
# TASKS="TRAIN FINETUNE ANALYZE"   # 执行全流程

TASKS="TRAIN"

# ==================== 读取配置文件 ====================
# 根据任务类型读取对应的配置文件
load_config() {
    local task_type=$1
    local config_file=""
    
    case $task_type in
        TRAIN)
            config_file="jobs/config/train.conf"
            ;;
        FINETUNE)
            config_file="jobs/config/finetune.conf"
            ;;
        ANALYZE)
            config_file="jobs/config/analyze.conf"
            ;;
        *)
            echo "错误: 未知任务类型: $task_type"
            return 1
            ;;
    esac
    
    if [ ! -f "$config_file" ]; then
        echo "错误: 配置文件 $config_file 不存在！"
        return 1
    fi
    
    echo "读取配置文件: $config_file"
    source "$config_file"
    return 0
}

# 进入工作目录（必须在读取配置文件之前）
cd $PBS_O_WORKDIR || exit $?

# 预加载所有可能用到的配置文件
echo "预加载配置文件..."
echo "当前工作目录: $(pwd)"
for task in $TASKS; do
    if ! load_config "$task"; then
        exit 1
    fi
done

# 记录作业开始时间和节点信息
echo "==================== 统一任务提交系统 ===================="
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "Selected tasks: $TASKS"
echo "GPU Information:"
nvidia-smi

# ==================== 环境配置 ====================
# conda环境名称
CONDA_ENV="netket"

# GPU设备
export CUDA_VISIBLE_DEVICES="0"

# 模块加载配置
ANACONDA_MODULE="anaconda2025/2025"
UNLOAD_CUDA_MODULE="cuda/12.2"

# 加载必要的模块
echo "Loading modules..."
module load $ANACONDA_MODULE
# 注意：anaconda2025会自动加载cuda/12.2作为依赖
# 如果需要其他CUDA版本，请先卸载cuda/12.2再加载所需版本
module unload $UNLOAD_CUDA_MODULE 2>/dev/null || true

# 初始化conda并激活环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate $CONDA_ENV

# 设置GPU设备
echo "Using GPU device: $CUDA_VISIBLE_DEVICES"

# 验证环境
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

# ==================== 通用函数定义 ====================

# 函数：检查checkpoint是否存在
check_checkpoint() {
    local Lx=$1
    local Ly=$2
    local Kx=$3
    local Ky=$4
    local Kz=$5
    local J=$6
    local hx=$7
    local hy=$8
    local hz=$9
    local Lambda=${10}
    local checkpoint_path="results/Lx=${Lx}_Ly=${Ly}/Kx=${Kx}_Ky=${Ky}_Kz=${Kz}/J=${J}/h=${hx}_${hy}_${hz}/Lambda=${Lambda}/training/checkpoints/final_cRBM.pkl"
    
    if [ -f "$checkpoint_path" ]; then
        echo "$checkpoint_path"
        return 0
    else
        return 1
    fi
}

# 函数：构建checkpoint参数
build_checkpoint_args() {
    local enable=$1
    local interval=$2
    local keep_history=$3
    local resume_from=$4
    
    local args=""
    if [ "$enable" = "true" ]; then
        args="--enable_checkpoint --save_interval $interval"
        
        if [ "$keep_history" = "true" ]; then
            args="$args --keep_history"
        fi
        
        if [ -n "$resume_from" ]; then
            args="$args --resume_from $resume_from"
        fi
    fi
    
    echo "$args"
}

# 函数：运行训练任务
run_train() {
    echo "==================== 执行训练任务 ===================="
    
    # 重新加载训练配置（确保使用最新配置）
    load_config "TRAIN"
    
    # 构建checkpoint参数
    local checkpoint_args=$(build_checkpoint_args \
        "$TRAIN_ENABLE_CHECKPOINT" \
        "$TRAIN_CHECKPOINT_INTERVAL" \
        "$TRAIN_KEEP_CHECKPOINT_HISTORY" \
        "$TRAIN_RESUME_FROM_CHECKPOINT")
    
    echo "训练参数配置:"
    echo "Lx values: $LX_VALUES"
    echo "Ly values: $LY_VALUES"
    echo "Kx values: $KX_VALUES"
    echo "Ky values: $KY_VALUES"
    echo "Kz values: $KZ_VALUES"
    echo "J values: $J_VALUES"
    echo "hx values: $HX_VALUES"
    echo "hy values: $HY_VALUES"
    echo "hz values: $HZ_VALUES"
    echo "Lambda values: $LAMBDA_VALUES"
    echo "Learning rate: $TRAIN_LEARNING_RATE"
    echo "Samples: $TRAIN_N_SAMPLES"
    echo "Checkpoint args: $checkpoint_args"
    
    for Lx in $LX_VALUES; do
        for Ly in $LY_VALUES; do
            for Kx in $KX_VALUES; do
                for Ky in $KY_VALUES; do
                    for Kz in $KZ_VALUES; do
                        for J in $J_VALUES; do
                            for hx in $HX_VALUES; do
                                for hy in $HY_VALUES; do
                                    for hz in $HZ_VALUES; do
                                        for Lambda in $LAMBDA_VALUES; do
                                            echo "Starting training Lx=$Lx, Ly=$Ly, Kx=$Kx, Ky=$Ky, Kz=$Kz, J=$J, hx=$hx, hy=$hy, hz=$hz, Lambda=$Lambda at: $(date)"

                                            # 顺序执行训练任务
                                            python scripts/train.py $Lx $Ly $Kx $Ky $Kz $J $hx $hy $hz $Lambda \
                                                --learning_rate $TRAIN_LEARNING_RATE \
                                                --n_samples $TRAIN_N_SAMPLES \
                                                --chunk_size $TRAIN_CHUNK_SIZE \
                                                --n_cycles $TRAIN_N_CYCLES \
                                                --initial_period $TRAIN_INITIAL_PERIOD \
                                                --period_mult $TRAIN_PERIOD_MULT \
                                                --max_temperature $TRAIN_MAX_TEMPERATURE \
                                                --min_temperature $TRAIN_MIN_TEMPERATURE \
                                                --alpha $TRAIN_ALPHA \
                                                --diag_shift $TRAIN_DIAG_SHIFT \
                                                --grad_clip $TRAIN_GRAD_CLIP \
                                                --reference_energy $TRAIN_REFERENCE_ENERGY \
                                                $checkpoint_args

                                            echo "Completed training job Lx=$Lx, Ly=$Ly, Kx=$Kx, Ky=$Ky, Kz=$Kz, J=$J, hx=$hx, hy=$hy, hz=$hz, Lambda=$Lambda at: $(date)"
                                        done
                                    done
                                done
                            done
                        done
                    done
                done
            done
        done
    done
    echo "所有训练任务已完成！"
}

# ==================== 主执行逻辑 ====================

# 并行执行选定的任务
task_pids=()

for task in $TASKS; do
    case $task in
        TRAIN)
            echo "启动训练任务..."
            run_train &
            task_pids+=($!)
            ;;
        FINETUNE)
            echo "微调任务暂未实现..."
            ;;
        ANALYZE)
            echo "分析任务暂未实现..."
            ;;
        *)
            echo "错误: 未知任务类型: $task"
            echo "可用任务类型: TRAIN, FINETUNE, ANALYZE"
            exit 1
            ;;
    esac
done

# 等待所有任务完成
echo "等待所有任务完成..."
for pid in "${task_pids[@]}"; do
    wait $pid
    echo "任务 PID $pid 已完成"
done

echo "==================== 所有任务完成 ===================="
echo "执行的任务: $TASKS"
echo "Job finished at: $(date)"
