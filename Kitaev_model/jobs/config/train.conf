#!/bin/bash
# 训练任务配置文件

# ==================== 系统参数 ====================
# 晶格尺寸和物理参数
LX_VALUES="4"
LY_VALUES="4"

# Kitaev相互作用参数
KX_VALUES="1.0"
KY_VALUES="1.0"
KZ_VALUES="1.0"

# Heisenberg相互作用
J_VALUES="0.0"

# [111]磁场
HX_VALUES="0.1"
HY_VALUES="0.1"
HZ_VALUES="0.1"

# 拉格朗日乘子
LAMBDA_VALUES="0.1"

# ==================== 训练参数 ====================
# 基础训练参数
TRAIN_LEARNING_RATE="0.1"
TRAIN_N_SAMPLES="8192"
TRAIN_CHUNK_SIZE="1024"

# 退火参数
TRAIN_N_CYCLES="1"
TRAIN_INITIAL_PERIOD="100"
TRAIN_PERIOD_MULT="2.0"
TRAIN_MAX_TEMPERATURE="1.0"
TRAIN_MIN_TEMPERATURE="0.01"

# 模型参数
TRAIN_ALPHA="4"
TRAIN_USE_HIDDEN_BIAS="true"
TRAIN_USE_VISIBLE_BIAS="true"

# 优化参数
TRAIN_DIAG_SHIFT="0.20"
TRAIN_GRAD_CLIP="1.0"

# 参考能量
TRAIN_REFERENCE_ENERGY="-6.396"

# ==================== Checkpoint配置 ====================
TRAIN_ENABLE_CHECKPOINT="true"
TRAIN_CHECKPOINT_INTERVAL="500"
TRAIN_KEEP_CHECKPOINT_HISTORY="true"
TRAIN_RESUME_FROM_CHECKPOINT=""  # 留空表示不从checkpoint恢复

# ==================== 输出配置 ====================
# 是否显示详细输出
TRAIN_VERBOSE="true"
