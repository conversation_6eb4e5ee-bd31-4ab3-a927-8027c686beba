#!/bin/bash
# 微调任务配置文件

# ==================== 系统参数 ====================
# 晶格尺寸（继承自全局配置）
# LX_VALUES 和 LY_VALUES 从全局配置继承

# ==================== 链式微调参数 ====================
# 微调的起始点和扩张范围
FINETUNE_START_KX="1.0"
FINETUNE_START_KY="1.0"
FINETUNE_START_KZ="1.0"
FINETUNE_START_J="0.0"

# Kitaev参数扩张范围
FINETUNE_KX_LEFT_BOUND="0.5"
FINETUNE_KX_RIGHT_BOUND="1.5"
FINETUNE_KX_STEP="0.1"

FINETUNE_KY_LEFT_BOUND="0.5"
FINETUNE_KY_RIGHT_BOUND="1.5"
FINETUNE_KY_STEP="0.1"

FINETUNE_KZ_LEFT_BOUND="0.5"
FINETUNE_KZ_RIGHT_BOUND="1.5"
FINETUNE_KZ_STEP="0.1"

# J参数扩张范围
FINETUNE_J_LEFT_BOUND="-0.5"
FINETUNE_J_RIGHT_BOUND="0.5"
FINETUNE_J_STEP="0.1"

# ==================== 微调训练参数 ====================
# 微调使用更小的学习率和更少的迭代
FINETUNE_LEARNING_RATE="0.05"
FINETUNE_N_SAMPLES="4096"
FINETUNE_CHUNK_SIZE="512"

# 微调退火参数
FINETUNE_N_CYCLES="1"
FINETUNE_INITIAL_PERIOD="50"
FINETUNE_PERIOD_MULT="1.5"
FINETUNE_MAX_TEMPERATURE="0.5"
FINETUNE_MIN_TEMPERATURE="0.01"

# 模型参数（继承自训练配置）
FINETUNE_ALPHA="4"
FINETUNE_USE_HIDDEN_BIAS="true"
FINETUNE_USE_VISIBLE_BIAS="true"

# 优化参数
FINETUNE_DIAG_SHIFT="0.10"
FINETUNE_GRAD_CLIP="0.5"

# ==================== Checkpoint配置 ====================
FINETUNE_ENABLE_CHECKPOINT="true"
FINETUNE_CHECKPOINT_INTERVAL="250"
FINETUNE_KEEP_CHECKPOINT_HISTORY="true"

# ==================== 输出配置 ====================
# 是否显示详细输出
FINETUNE_VERBOSE="true"
