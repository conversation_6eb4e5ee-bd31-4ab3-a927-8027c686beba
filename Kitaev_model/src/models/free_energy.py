#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kitaev模型自由能训练模块
提供类似Shastry-Sutherland模型的CustomFreeEnergyVMC_SR功能
"""

import time
import jax
import jax.numpy as jnp
import netket as nk
import netket.optimizer as nk_opt
from netket.optimizer.qgt import QGTJacobianDense
from src.utils.logging import log_message

class CustomFreeEnergyVMC_SR:
    """
    自定义自由能变分蒙特卡洛随机重配置优化器
    实现F = E - TS的最小化，支持热重启和checkpoint
    """
    
    def __init__(self, reference_energy=None, initial_period=100, period_mult=2.0,
                 max_temperature=1.0, min_temperature=0.01, clip_norm=1.0,
                 checkpoint_callback=None, checkpoint_interval=500,
                 hamiltonian=None, optimizer=None, diag_shift=0.01,
                 variational_state=None):
        """
        初始化自由能优化器
        
        Args:
            reference_energy: 参考能量，用于计算相对误差
            initial_period: 初始周期长度
            period_mult: 周期倍数
            max_temperature: 最大温度
            min_temperature: 最小温度
            clip_norm: 梯度裁剪范数
            checkpoint_callback: checkpoint回调函数
            checkpoint_interval: checkpoint保存间隔
            hamiltonian: 哈密顿量
            optimizer: 优化器
            diag_shift: 对角线位移
            variational_state: 变分量子态
        """
        self.reference_energy = reference_energy
        self.initial_period = initial_period
        self.period_mult = period_mult
        self.max_temperature = max_temperature
        self.min_temperature = min_temperature
        self.clip_norm = clip_norm
        self.checkpoint_callback = checkpoint_callback
        self.checkpoint_interval = checkpoint_interval
        
        self.hamiltonian = hamiltonian
        self.optimizer = optimizer
        self.diag_shift = diag_shift
        self.vstate = variational_state
        
        # 初始化QGT
        self.qgt = QGTJacobianDense(diag_shift=diag_shift)
        
    def _temperature_schedule(self, iteration, n_iter):
        """计算当前迭代的温度"""
        # 简单的线性退火
        progress = iteration / n_iter
        return self.max_temperature * (1 - progress) + self.min_temperature * progress
    
    def _compute_free_energy_gradient(self, params, temperature):
        """计算自由能梯度"""
        # 计算能量梯度
        energy_grad = self.vstate.expect_and_grad(self.hamiltonian)[1]
        
        # 计算熵梯度项（简化版本）
        # 这里使用简化的熵梯度计算
        entropy_grad = jax.tree_map(lambda x: jnp.zeros_like(x), params)
        
        # 组合自由能梯度: ∇F = ∇E - T∇S
        free_energy_grad = jax.tree_map(
            lambda e_grad, s_grad: e_grad - temperature * s_grad,
            energy_grad, entropy_grad
        )
        
        return free_energy_grad
    
    def _apply_gradient_clipping(self, grad):
        """应用梯度裁剪"""
        if self.clip_norm is None or self.clip_norm <= 0:
            return grad
        
        # 计算梯度范数
        grad_norm = jnp.sqrt(sum(jnp.sum(jnp.abs(g)**2) for g in jax.tree_leaves(grad)))
        
        # 如果梯度范数超过阈值，进行裁剪
        if grad_norm > self.clip_norm:
            clip_factor = self.clip_norm / grad_norm
            grad = jax.tree_map(lambda g: g * clip_factor, grad)
        
        return grad
    
    def run(self, n_iter, energy_log):
        """
        运行自由能优化
        
        Args:
            n_iter: 迭代次数
            energy_log: 日志文件路径
        """
        log_message(energy_log, "开始自由能优化...")
        
        # 初始化优化器状态
        opt_state = self.optimizer.init(self.vstate.parameters)
        
        for i in range(n_iter):
            # 计算当前温度
            temperature = self._temperature_schedule(i, n_iter)
            
            # 计算能量和误差
            energy = self.vstate.expect(self.hamiltonian)
            energy_mean = energy.mean
            energy_error = energy.error_of_mean
            
            # 计算自由能梯度
            free_energy_grad = self._compute_free_energy_gradient(self.vstate.parameters, temperature)
            
            # 应用梯度裁剪
            free_energy_grad = self._apply_gradient_clipping(free_energy_grad)
            
            # 使用QGT进行自然梯度更新
            # 这里简化为直接使用梯度，实际应该使用QGT
            updates, opt_state = self.optimizer.update(free_energy_grad, opt_state, self.vstate.parameters)
            self.vstate.parameters = nk_opt.apply_updates(self.vstate.parameters, updates)
            
            # 记录日志
            if i % 10 == 0 or i == n_iter - 1:
                log_line = f"Iter {i:6d}: E = {energy_mean:.6f} ± {energy_error:.6f}, T = {temperature:.4f}"
                
                # 如果有参考能量，计算相对误差
                if self.reference_energy is not None:
                    relative_error = abs(energy_mean - self.reference_energy) / abs(self.reference_energy)
                    log_line += f", Rel. Err. = {relative_error:.6f}"
                
                log_message(energy_log, log_line)
            
            # 保存checkpoint
            if (self.checkpoint_callback is not None and 
                self.checkpoint_interval > 0 and 
                (i + 1) % self.checkpoint_interval == 0):
                self.checkpoint_callback(i + 1, energy_mean, energy_error)
        
        log_message(energy_log, "自由能优化完成")

def T_logp2(params, inputs, temperature, model):
    """计算熵梯度项 T * <(log ψ)²>"""
    variables = {"params": params}
    preds = model.apply(variables, inputs)
    return 2.0 * temperature * jnp.mean(jnp.real(preds) * jnp.real(preds))

def T_logp_2(params, inputs, temperature, model):
    """计算熵梯度项 T * <log ψ>²"""
    variables = {"params": params}
    preds = model.apply(variables, inputs)
    return 2.0 * temperature * jnp.mean(jnp.real(preds)) * jnp.mean(jnp.real(preds))

def cleanup_energy_files(result_dir, Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz):
    """清理旧的能量文件"""
    import os
    import glob
    
    # 构建文件模式
    pattern = os.path.join(result_dir, f"energy_Lx={Lx}_Ly={Ly}_Kx={Kx}_Ky={Ky}_Kz={Kz}_J={J}_h={hx}_{hy}_{hz}_*.log")
    
    # 查找并删除匹配的文件
    for file_path in glob.glob(pattern):
        try:
            os.remove(file_path)
            print(f"已删除旧文件: {file_path}")
        except OSError as e:
            print(f"删除文件失败 {file_path}: {e}")
