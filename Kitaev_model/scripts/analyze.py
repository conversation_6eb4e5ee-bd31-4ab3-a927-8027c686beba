#!/usr/bin/env python3
"""
Kitaev模型分析脚本
用于分析训练结果和checkpoint文件
"""

# 设置环境变量
import os
import sys
import argparse
import pickle
import json

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"

import jax
import netket as nk
import numpy as np

# 导入自定义模块
from src.runner import KitaevRunner
from src.utils.logging import log_message

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(description='Kitaev模型分析')

    # 系统参数
    parser.add_argument('--Lx', type=int, required=True, help='x方向晶格尺寸')
    parser.add_argument('--Ly', type=int, required=True, help='y方向晶格尺寸')
    parser.add_argument('--Kx', type=float, required=True, help='x方向Kitaev耦合强度')
    parser.add_argument('--Ky', type=float, required=True, help='y方向Kitaev耦合强度')
    parser.add_argument('--Kz', type=float, required=True, help='z方向Kitaev耦合强度')
    parser.add_argument('--J', type=float, required=True, help='Heisenberg耦合强度')
    parser.add_argument('--hx', type=float, required=True, help='x方向磁场')
    parser.add_argument('--hy', type=float, required=True, help='y方向磁场')
    parser.add_argument('--hz', type=float, required=True, help='z方向磁场')
    parser.add_argument('--Lambda', type=float, required=True, help='拉格朗日乘子')

    # 分析参数
    parser.add_argument('--checkpoint', type=str, required=True, help='checkpoint文件名（不含路径和扩展名）')
    parser.add_argument('--n_samples', type=int, default=16384, help='分析用样本数量')

    return parser

def load_checkpoint_data(checkpoint_path):
    """加载checkpoint数据"""
    try:
        with open(checkpoint_path, "rb") as f:
            checkpoint_data = pickle.load(f)
        return checkpoint_data
    except Exception as e:
        print(f"加载checkpoint失败: {e}")
        return None

def analyze_checkpoint(args):
    """分析单个checkpoint"""
    # 构建checkpoint路径
    result_dir = f"results/Lx={args.Lx}_Ly={args.Ly}/Kx={args.Kx}_Ky={args.Ky}_Kz={args.Kz}/J={args.J}/h={args.hx}_{args.hy}_{args.hz}/Lambda={args.Lambda}/training"
    checkpoint_dir = os.path.join(result_dir, "checkpoints")
    checkpoint_path = os.path.join(checkpoint_dir, f"{args.checkpoint}.pkl")
    
    if not os.path.exists(checkpoint_path):
        print(f"错误: Checkpoint文件不存在: {checkpoint_path}")
        return None
    
    # 加载checkpoint数据
    checkpoint_data = load_checkpoint_data(checkpoint_path)
    if checkpoint_data is None:
        return None
    
    print("="*60)
    print("Kitaev模型分析")
    print("="*60)
    print(f"系统参数: Lx={args.Lx}, Ly={args.Ly}")
    print(f"Kitaev耦合: Kx={args.Kx}, Ky={args.Ky}, Kz={args.Kz}")
    print(f"Heisenberg耦合: J={args.J}")
    print(f"磁场: hx={args.hx}, hy={args.hy}, hz={args.hz}")
    print(f"拉格朗日乘子: Lambda={args.Lambda}")
    print(f"分析checkpoint: {args.checkpoint}")
    print("="*60)
    
    # 创建分析目录
    analysis_dir = os.path.join(os.path.dirname(result_dir), "analysis")
    os.makedirs(analysis_dir, exist_ok=True)
    
    # 设置分析日志
    analysis_log = os.path.join(analysis_dir, f"analysis_{args.checkpoint}.log")
    
    try:
        # 重建运行器以进行分析
        runner = KitaevRunner(
            Lx=args.Lx, Ly=args.Ly, Kx=args.Kx, Ky=args.Ky, Kz=args.Kz, J=args.J,
            hx=args.hx, hy=args.hy, hz=args.hz, Lambda=args.Lambda,
            model_class=checkpoint_data['model_type'],
            model_config=checkpoint_data['model_config'],
            training_config=checkpoint_data['training_config'],
            output_dir=result_dir
        )
        
        # 设置模型
        runner.setup_model()
        
        # 恢复参数
        runner.vqs.parameters = checkpoint_data['parameters']
        
        log_message(analysis_log, "="*50)
        log_message(analysis_log, f"分析checkpoint: {args.checkpoint}")
        log_message(analysis_log, "="*50)
        log_message(analysis_log, f"迭代次数: {checkpoint_data['iteration']}")
        log_message(analysis_log, f"时间戳: {checkpoint_data['timestamp']}")
        
        # 计算能量
        log_message(analysis_log, "计算能量...")
        energy = runner.vqs.expect(runner.H)
        log_message(analysis_log, f"能量: {energy.mean:.6f} ± {energy.error_of_mean:.6f}")
        
        # 如果有参考能量，计算相对误差
        if 'reference_energy' in checkpoint_data.get('training_config', {}):
            ref_energy = checkpoint_data['training_config']['reference_energy']
            if ref_energy is not None:
                relative_error = abs(energy.mean - ref_energy) / abs(ref_energy)
                log_message(analysis_log, f"参考能量: {ref_energy}")
                log_message(analysis_log, f"相对误差: {relative_error:.6f}")
        
        # 保存分析结果
        analysis_results = {
            'checkpoint': args.checkpoint,
            'iteration': checkpoint_data['iteration'],
            'timestamp': checkpoint_data['timestamp'],
            'energy_mean': float(energy.mean),
            'energy_error': float(energy.error_of_mean),
            'system_parameters': {
                'Lx': args.Lx, 'Ly': args.Ly,
                'Kx': args.Kx, 'Ky': args.Ky, 'Kz': args.Kz,
                'J': args.J, 'hx': args.hx, 'hy': args.hy, 'hz': args.hz,
                'Lambda': args.Lambda
            }
        }
        
        results_file = os.path.join(analysis_dir, f"results_{args.checkpoint}.json")
        with open(results_file, "w") as f:
            json.dump(analysis_results, f, indent=4)
        
        log_message(analysis_log, f"分析结果已保存到: {results_file}")
        log_message(analysis_log, "="*50)
        
        print(f"分析完成！结果保存在: {analysis_dir}")
        return analysis_results
        
    except Exception as e:
        print(f"分析失败: {e}")
        log_message(analysis_log, f"分析失败: {e}")
        return None

def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    print(f"JAX设备: {jax.devices()}")
    print(f"设备数量: {len(jax.devices())}")
    print("="*60)
    
    try:
        results = analyze_checkpoint(args)
        if results:
            print("="*60)
            print("分析完成！")
            print("="*60)
        else:
            print("分析失败！")
            sys.exit(1)
    except Exception as e:
        print(f"分析过程中发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    main()
