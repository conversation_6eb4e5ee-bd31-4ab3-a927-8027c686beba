#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
入口点脚本，用于启动Kitaev模型训练
兼容性脚本 - 建议使用scripts/train.py获得更多功能
"""

import os
import sys
from scripts import main

if __name__ == "__main__":
    # 直接将参数传递给main模块
    if len(sys.argv) > 1:
        print("注意: 使用兼容性模式运行")
        print("建议使用: python scripts/train.py 获得更多功能和配置选项")
        print("="*60)
        main.main()
    else:
        print("使用方法:")
        print("  兼容性模式: python run.py <Lx> <Ly> <Kx> <Ky> <Kz> <J> <hx> <hy> <hz> <Lambda>")
        print("  推荐方式:   python scripts/train.py <Lx> <Ly> <Kx> <Ky> <Kz> <J> <hx> <hy> <hz> <Lambda> [选项]")
        print("")
        print("例如:")
        print("  python run.py 4 4 1.0 1.0 1.0 0.0 0.1 0.1 0.1 0.1")
        print("  python scripts/train.py 4 4 1.0 1.0 1.0 0.0 0.1 0.1 0.1 0.1 --enable_checkpoint")
        sys.exit(1)