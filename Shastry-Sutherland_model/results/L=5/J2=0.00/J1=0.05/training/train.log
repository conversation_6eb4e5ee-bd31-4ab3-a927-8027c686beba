[2025-09-03 14:33:17] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-09-03 14:33:17]   - 迭代次数: final
[2025-09-03 14:33:17]   - 能量: -85.089073+0.000976j ± 0.056522
[2025-09-03 14:33:17]   - 时间戳: 2025-09-02T21:33:23.744915+08:00
[2025-09-03 14:33:28] ✓ 变分状态参数已从checkpoint恢复
[2025-09-03 14:33:28] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-03 14:33:28] ==================================================
[2025-09-03 14:33:28] GCNN for Shastry-Sutherland Model
[2025-09-03 14:33:28] ==================================================
[2025-09-03 14:33:28] System parameters:
[2025-09-03 14:33:28]   - System size: L=5, N=100
[2025-09-03 14:33:28]   - System parameters: J1=0.05, J2=0.0, Q=1.0
[2025-09-03 14:33:28] --------------------------------------------------
[2025-09-03 14:33:28] Model parameters:
[2025-09-03 14:33:28]   - Number of layers = 4
[2025-09-03 14:33:28]   - Number of features = 4
[2025-09-03 14:33:28]   - Total parameters = 19628
[2025-09-03 14:33:28] --------------------------------------------------
[2025-09-03 14:33:28] Training parameters:
[2025-09-03 14:33:28]   - Learning rate: 0.015
[2025-09-03 14:33:28]   - Total iterations: 450
[2025-09-03 14:33:28]   - Annealing cycles: 2
[2025-09-03 14:33:28]   - Initial period: 150
[2025-09-03 14:33:28]   - Period multiplier: 2.0
[2025-09-03 14:33:28]   - Temperature range: 0.0-1.0
[2025-09-03 14:33:28]   - Samples: 4096
[2025-09-03 14:33:28]   - Discarded samples: 0
[2025-09-03 14:33:28]   - Chunk size: 2048
[2025-09-03 14:33:28]   - Diagonal shift: 0.2
[2025-09-03 14:33:28]   - Gradient clipping: 1.0
[2025-09-03 14:33:28]   - Checkpoint enabled: interval=50
[2025-09-03 14:33:28]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.05/training/checkpoints
[2025-09-03 14:33:28] --------------------------------------------------
[2025-09-03 14:33:28] Device status:
[2025-09-03 14:33:28]   - Devices model: NVIDIA H200 NVL
[2025-09-03 14:33:28]   - Number of devices: 1
[2025-09-03 14:33:28]   - Sharding: True
[2025-09-03 14:33:28] ============================================================
[2025-09-03 14:34:25] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -85.814568-0.016146j
[2025-09-03 14:35:11] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -85.668552+0.000471j
[2025-09-03 14:35:42] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -85.806141-0.010480j
[2025-09-03 14:36:13] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -85.733463-0.007117j
[2025-09-03 14:36:44] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -85.699006+0.001053j
[2025-09-03 14:37:15] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -85.628531-0.004160j
[2025-09-03 14:37:46] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -85.628508+0.002558j
[2025-09-03 14:38:17] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -85.665920-0.003022j
[2025-09-03 14:38:47] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -85.692084-0.006363j
[2025-09-03 14:39:18] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -85.624297-0.007298j
[2025-09-03 14:39:49] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -85.650900-0.006855j
[2025-09-03 14:40:20] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -85.479166-0.011572j
[2025-09-03 14:40:51] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -85.671883+0.007732j
[2025-09-03 14:41:22] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -85.660193-0.006235j
[2025-09-03 14:41:53] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -85.620915-0.002338j
[2025-09-03 14:42:24] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -85.598475-0.008436j
[2025-09-03 14:42:55] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -85.605779+0.003058j
[2025-09-03 14:43:26] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -85.749425-0.004829j
[2025-09-03 14:43:56] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -85.709632-0.004780j
[2025-09-03 14:44:27] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -85.648085+0.003446j
[2025-09-03 14:44:58] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -85.677003+0.000004j
[2025-09-03 14:45:29] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -85.547321-0.000566j
[2025-09-03 14:46:00] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -85.479892+0.001613j
[2025-09-03 14:46:31] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -85.569680-0.001153j
[2025-09-03 14:47:02] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -85.557836-0.002363j
[2025-09-03 14:47:33] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -85.466335+0.004788j
[2025-09-03 14:48:05] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -85.537582-0.007885j
[2025-09-03 14:48:36] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -85.544718+0.001056j
[2025-09-03 14:49:07] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -85.668210+0.006795j
[2025-09-03 14:49:38] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -85.563842-0.002425j
[2025-09-03 14:50:09] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -85.513963-0.000201j
[2025-09-03 14:50:40] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -85.556358+0.000907j
[2025-09-03 14:51:11] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -85.648470-0.003684j
[2025-09-03 14:51:42] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -85.568166+0.002320j
[2025-09-03 14:52:14] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -85.645042+0.000906j
[2025-09-03 14:52:45] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -85.783786+0.002083j
[2025-09-03 14:53:16] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -85.927632-0.002470j
[2025-09-03 14:53:47] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -85.846929+0.002752j
[2025-09-03 14:54:18] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -85.909647+0.000927j
[2025-09-03 14:54:49] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -85.566112+0.001077j
[2025-09-03 14:55:21] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -85.529456+0.000824j
[2025-09-03 14:55:52] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -85.641384-0.000544j
[2025-09-03 14:56:23] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -85.591800+0.000907j
[2025-09-03 14:56:54] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -85.497372+0.003208j
[2025-09-03 14:57:25] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -85.491415+0.004847j
[2025-09-03 14:57:56] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -85.503301+0.000508j
[2025-09-03 14:58:27] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -85.693395-0.000699j
[2025-09-03 14:58:58] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -85.607323-0.005883j
[2025-09-03 14:59:29] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -85.731232-0.004470j
[2025-09-03 15:00:00] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -85.605797+0.001702j
[2025-09-03 15:00:00] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-09-03 15:00:31] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -85.612454-0.002217j
[2025-09-03 15:01:02] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -85.772512+0.002373j
[2025-09-03 15:01:33] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -85.777613-0.000059j
[2025-09-03 15:02:04] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -85.794362-0.002269j
[2025-09-03 15:02:34] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -85.789538-0.011232j
[2025-09-03 15:03:05] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -85.705981-0.004086j
[2025-09-03 15:03:36] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -85.666815+0.004577j
[2025-09-03 15:04:07] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -85.771608-0.005491j
[2025-09-03 15:04:38] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -85.787858-0.000251j
[2025-09-03 15:05:09] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -85.773008+0.002554j
[2025-09-03 15:05:40] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -85.613440+0.003826j
[2025-09-03 15:06:10] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -85.602957-0.001559j
[2025-09-03 15:06:41] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -85.494838-0.012863j
[2025-09-03 15:07:12] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -85.547673+0.003913j
[2025-09-03 15:07:43] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -85.459737+0.003639j
[2025-09-03 15:08:14] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -85.484729+0.003668j
[2025-09-03 15:08:45] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -85.522022+0.002715j
[2025-09-03 15:09:16] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -85.476671-0.002169j
[2025-09-03 15:09:47] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -85.522842+0.005257j
[2025-09-03 15:10:18] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -85.648625+0.002305j
[2025-09-03 15:10:48] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -85.533172-0.007171j
[2025-09-03 15:11:19] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -85.531600-0.001383j
[2025-09-03 15:11:50] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -85.616889+0.002923j
[2025-09-03 15:12:21] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -85.698134-0.000285j
[2025-09-03 15:12:52] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -85.686768+0.000554j
[2025-09-03 15:13:23] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -85.694676+0.000532j
[2025-09-03 15:13:54] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -85.691884+0.004937j
[2025-09-03 15:14:25] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -85.782485+0.003007j
[2025-09-03 15:14:56] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -85.823893-0.008687j
[2025-09-03 15:15:27] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -85.728573-0.004679j
[2025-09-03 15:15:57] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -85.579521+0.002466j
[2025-09-03 15:16:28] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -85.638195-0.002233j
[2025-09-03 15:16:59] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -85.677194-0.004979j
[2025-09-03 15:17:30] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -85.461325+0.004429j
[2025-09-03 15:18:01] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -85.423450-0.004267j
[2025-09-03 15:18:32] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -85.614626-0.005134j
[2025-09-03 15:19:03] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -85.626773+0.002362j
[2025-09-03 15:19:34] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -85.522973-0.002074j
[2025-09-03 15:20:04] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -85.722034-0.002263j
[2025-09-03 15:20:35] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -85.762808+0.003654j
[2025-09-03 15:21:06] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -85.665884-0.002440j
[2025-09-03 15:21:37] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -85.649157-0.002610j
[2025-09-03 15:22:08] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -85.650896-0.008207j
[2025-09-03 15:22:39] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -85.520940+0.000737j
[2025-09-03 15:23:10] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -85.551453-0.005684j
[2025-09-03 15:23:41] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -85.548730-0.001631j
[2025-09-03 15:24:12] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -85.510828+0.002744j
[2025-09-03 15:24:43] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -85.644392-0.000517j
[2025-09-03 15:25:13] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -85.726192-0.001519j
[2025-09-03 15:25:44] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -85.664222+0.001294j
[2025-09-03 15:25:44] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-09-03 15:26:15] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -85.579696+0.000493j
[2025-09-03 15:26:46] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -85.558716+0.001648j
[2025-09-03 15:27:17] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -85.608108-0.003927j
[2025-09-03 15:27:48] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -85.578953-0.003567j
[2025-09-03 15:28:19] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -85.724296-0.005342j
[2025-09-03 15:28:50] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -85.651039-0.002975j
[2025-09-03 15:29:21] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -85.675002+0.003762j
[2025-09-03 15:29:52] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -85.664213+0.005175j
[2025-09-03 15:30:23] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -85.804794+0.000714j
[2025-09-03 15:30:53] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -85.760279-0.006130j
[2025-09-03 15:31:24] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -85.532354-0.002224j
[2025-09-03 15:31:55] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -85.623491-0.005010j
[2025-09-03 15:32:26] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -85.569695+0.004209j
[2025-09-03 15:32:57] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -85.718795+0.004700j
[2025-09-03 15:33:28] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -85.685337+0.005080j
[2025-09-03 15:33:59] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -85.768436+0.001281j
[2025-09-03 15:34:30] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -85.793036+0.003894j
[2025-09-03 15:35:01] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -85.788184-0.002883j
[2025-09-03 15:35:32] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -85.786667-0.011364j
[2025-09-03 15:36:03] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -85.670264+0.005068j
[2025-09-03 15:36:34] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -85.773731+0.011022j
[2025-09-03 15:37:05] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -85.653419-0.001284j
[2025-09-03 15:37:36] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -85.668563+0.002369j
[2025-09-03 15:38:07] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -85.651510-0.000210j
[2025-09-03 15:38:37] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -85.731606-0.003735j
[2025-09-03 15:39:08] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -85.812203-0.007347j
[2025-09-03 15:39:39] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -85.748481-0.002712j
[2025-09-03 15:40:10] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -85.575937-0.004212j
[2025-09-03 15:40:41] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -85.520720-0.000934j
[2025-09-03 15:41:12] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -85.461853+0.003374j
[2025-09-03 15:41:43] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -85.527926+0.003419j
[2025-09-03 15:42:14] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -85.798427-0.008896j
[2025-09-03 15:42:45] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -85.800259-0.005860j
[2025-09-03 15:43:16] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -85.594742+0.002398j
[2025-09-03 15:43:46] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -85.653364+0.002731j
[2025-09-03 15:44:17] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -85.707695+0.000417j
[2025-09-03 15:44:48] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -85.608966-0.004165j
[2025-09-03 15:45:19] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -85.591891-0.000576j
[2025-09-03 15:45:50] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -85.625999+0.001909j
[2025-09-03 15:46:21] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -85.484735-0.000253j
[2025-09-03 15:46:52] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -85.578444-0.006102j
[2025-09-03 15:47:23] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -85.649680-0.000904j
[2025-09-03 15:47:54] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -85.684199-0.007832j
[2025-09-03 15:48:25] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -85.729462+0.001685j
[2025-09-03 15:48:56] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -85.713292-0.004316j
[2025-09-03 15:49:26] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -85.644258+0.009735j
[2025-09-03 15:49:57] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -85.638196-0.001613j
[2025-09-03 15:50:28] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -85.570223+0.004718j
[2025-09-03 15:50:59] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -85.597421-0.000610j
[2025-09-03 15:51:30] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -85.597060-0.003234j
[2025-09-03 15:51:30] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-09-03 15:51:30] RESTART #1 | Period: 300
[2025-09-03 15:52:01] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -85.678252+0.006213j
[2025-09-03 15:52:32] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -85.628295+0.001296j
[2025-09-03 15:53:03] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -85.554685-0.005975j
[2025-09-03 15:53:34] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -85.672486-0.000417j
[2025-09-03 15:54:05] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -85.566501+0.004862j
[2025-09-03 15:54:36] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -85.372428+0.001818j
[2025-09-03 15:55:07] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -85.316557+0.000403j
[2025-09-03 15:55:37] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -85.538649+0.003175j
[2025-09-03 15:56:08] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -85.616735+0.004827j
[2025-09-03 15:56:39] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -85.684195-0.004108j
[2025-09-03 15:57:10] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -85.467156-0.000681j
[2025-09-03 15:57:41] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -85.573861+0.000013j
[2025-09-03 15:58:12] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -85.677660-0.005497j
[2025-09-03 15:58:43] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -85.553139-0.002004j
[2025-09-03 15:59:14] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -85.593087-0.003215j
[2025-09-03 15:59:45] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -85.795317+0.000541j
[2025-09-03 16:00:16] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -85.556097+0.003069j
[2025-09-03 16:00:46] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -85.591569+0.005650j
[2025-09-03 16:01:17] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -85.703211+0.004623j
[2025-09-03 16:01:48] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -85.555158-0.001416j
[2025-09-03 16:02:19] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -85.612978+0.007061j
[2025-09-03 16:02:50] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -85.547487-0.003883j
[2025-09-03 16:03:21] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -85.595396+0.003232j
[2025-09-03 16:03:52] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -85.592331+0.001691j
[2025-09-03 16:04:23] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -85.698207-0.001029j
[2025-09-03 16:04:54] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -85.658110-0.001961j
[2025-09-03 16:05:25] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -85.658175-0.006276j
[2025-09-03 16:05:56] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -85.815046+0.009168j
[2025-09-03 16:06:27] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -85.773810-0.002729j
[2025-09-03 16:06:58] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -85.520476-0.000705j
[2025-09-03 16:07:29] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -85.489504-0.005101j
[2025-09-03 16:07:59] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -85.521654+0.001397j
[2025-09-03 16:08:30] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -85.513120-0.009835j
[2025-09-03 16:09:01] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -85.660826+0.005775j
[2025-09-03 16:09:32] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -85.552736-0.003092j
[2025-09-03 16:10:03] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -85.547076-0.004787j
[2025-09-03 16:10:34] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -85.294209+0.000052j
[2025-09-03 16:11:05] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -85.291298+0.000092j
[2025-09-03 16:11:36] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -85.496895+0.001078j
[2025-09-03 16:12:07] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -85.529557-0.001465j
[2025-09-03 16:12:38] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -85.646564+0.002752j
[2025-09-03 16:13:09] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -85.416991+0.006617j
[2025-09-03 16:13:40] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -85.514483-0.006050j
[2025-09-03 16:14:12] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -85.499374+0.005721j
[2025-09-03 16:14:43] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -85.389432+0.003227j
[2025-09-03 16:15:14] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -85.436159-0.000494j
[2025-09-03 16:15:45] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -85.474610+0.001912j
[2025-09-03 16:16:16] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -85.580751+0.009344j
[2025-09-03 16:16:47] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -85.584557+0.007134j
[2025-09-03 16:17:18] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -85.741678+0.009541j
[2025-09-03 16:17:18] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-09-03 16:17:49] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -85.631206+0.003286j
[2025-09-03 16:18:20] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -85.549863-0.004049j
[2025-09-03 16:18:51] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -85.562423+0.003920j
[2025-09-03 16:19:22] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -85.480669+0.001603j
[2025-09-03 16:19:53] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -85.431275+0.000321j
[2025-09-03 16:20:24] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -85.391646+0.002280j
[2025-09-03 16:20:55] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -85.634158-0.002883j
[2025-09-03 16:21:27] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -85.702668-0.001609j
[2025-09-03 16:21:58] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -85.670188+0.001505j
[2025-09-03 16:22:29] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -85.630378-0.000073j
[2025-09-03 16:23:00] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -85.653099+0.001763j
[2025-09-03 16:23:31] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -85.551885+0.001074j
[2025-09-03 16:24:02] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -85.707540-0.000429j
[2025-09-03 16:24:33] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -85.614608+0.001797j
[2025-09-03 16:25:04] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -85.571754-0.003781j
[2025-09-03 16:25:35] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -85.732381-0.000367j
[2025-09-03 16:26:06] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -85.724238-0.000875j
[2025-09-03 16:26:37] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -85.693122-0.001853j
[2025-09-03 16:27:08] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -85.687526+0.001442j
[2025-09-03 16:27:39] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -85.590223+0.000043j
[2025-09-03 16:28:10] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -85.548088+0.001044j
[2025-09-03 16:28:41] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -85.788655+0.001991j
[2025-09-03 16:29:12] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -85.696167-0.001621j
[2025-09-03 16:29:44] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -85.438589-0.003623j
[2025-09-03 16:30:15] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -85.461667-0.001210j
[2025-09-03 16:30:46] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -85.555651-0.001188j
[2025-09-03 16:31:17] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -85.521285+0.003555j
[2025-09-03 16:31:48] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -85.527951+0.006921j
[2025-09-03 16:32:19] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -85.559801+0.004837j
[2025-09-03 16:32:50] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -85.491940+0.001348j
[2025-09-03 16:33:21] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -85.577959+0.003516j
[2025-09-03 16:33:52] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -85.592739+0.003999j
[2025-09-03 16:34:23] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -85.738125-0.001596j
[2025-09-03 16:34:54] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -85.800943+0.000528j
[2025-09-03 16:35:25] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -85.761945-0.000837j
[2025-09-03 16:35:56] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -85.707434-0.001196j
[2025-09-03 16:36:27] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -85.582772+0.004194j
[2025-09-03 16:36:58] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -85.756195-0.000457j
[2025-09-03 16:37:29] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -85.676784+0.001000j
[2025-09-03 16:38:00] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -85.775330+0.001951j
[2025-09-03 16:38:31] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -85.701671+0.005296j
[2025-09-03 16:39:03] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -85.633922+0.003692j
[2025-09-03 16:39:34] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -85.739254-0.004639j
[2025-09-03 16:40:05] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -85.735093+0.002231j
[2025-09-03 16:40:36] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -85.661144+0.003533j
[2025-09-03 16:41:07] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -85.600297+0.002786j
[2025-09-03 16:41:38] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -85.589696+0.003779j
[2025-09-03 16:42:09] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -85.627478-0.000725j
[2025-09-03 16:42:40] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -85.638108+0.003765j
[2025-09-03 16:43:11] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -85.729757+0.005704j
[2025-09-03 16:43:11] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-03 16:43:42] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -85.695221-0.000920j
[2025-09-03 16:44:13] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -85.646772-0.005512j
[2025-09-03 16:44:44] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -85.407347-0.002404j
[2025-09-03 16:45:15] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -85.490605+0.003373j
[2025-09-03 16:45:46] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -85.516207+0.002303j
[2025-09-03 16:46:17] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -85.490569+0.007180j
[2025-09-03 16:46:48] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -85.530792+0.007327j
[2025-09-03 16:47:19] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -85.523477+0.000290j
[2025-09-03 16:47:50] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -85.471360+0.002083j
[2025-09-03 16:48:21] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -85.538200-0.003225j
[2025-09-03 16:48:53] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -85.590095+0.001102j
[2025-09-03 16:49:24] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -85.713841+0.001111j
[2025-09-03 16:49:55] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -85.631535-0.005058j
[2025-09-03 16:50:26] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -85.581021+0.003666j
[2025-09-03 16:50:57] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -85.541896+0.004142j
[2025-09-03 16:51:28] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -85.564745-0.002476j
[2025-09-03 16:51:59] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -85.525979-0.000034j
[2025-09-03 16:52:30] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -85.528050+0.004410j
[2025-09-03 16:53:01] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -85.599131+0.004746j
[2025-09-03 16:53:32] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -85.669180+0.001459j
[2025-09-03 16:54:03] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -85.622311-0.004850j
[2025-09-03 16:54:34] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -85.515624-0.003721j
[2025-09-03 16:55:05] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -85.507670-0.012045j
[2025-09-03 16:55:36] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -85.541847+0.001538j
[2025-09-03 16:56:07] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -85.608715-0.002362j
[2025-09-03 16:56:38] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -85.482551+0.000296j
[2025-09-03 16:57:09] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -85.597574+0.003542j
[2025-09-03 16:57:40] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -85.540955+0.000088j
[2025-09-03 16:58:12] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -85.460735+0.004203j
[2025-09-03 16:58:43] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -85.557473-0.003079j
[2025-09-03 16:59:14] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -85.532714-0.005325j
[2025-09-03 16:59:45] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -85.456507+0.001567j
[2025-09-03 17:00:16] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -85.501242+0.002527j
[2025-09-03 17:00:47] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -85.421816+0.002188j
[2025-09-03 17:01:18] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -85.612818+0.000241j
[2025-09-03 17:01:49] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -85.558114-0.004244j
[2025-09-03 17:02:20] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -85.677281-0.004062j
[2025-09-03 17:02:51] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -85.674844-0.005500j
[2025-09-03 17:03:22] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -85.742725+0.007217j
[2025-09-03 17:03:53] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -85.534885+0.001160j
[2025-09-03 17:04:24] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -85.668505+0.004466j
[2025-09-03 17:04:55] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -85.630798+0.003223j
[2025-09-03 17:05:26] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -85.580723-0.005749j
[2025-09-03 17:05:57] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -85.743691+0.001575j
[2025-09-03 17:06:28] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -85.589134+0.004188j
[2025-09-03 17:06:59] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -85.531512+0.002058j
[2025-09-03 17:07:30] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -85.580858+0.001354j
[2025-09-03 17:08:01] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -85.634266+0.000117j
[2025-09-03 17:08:33] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -85.663885-0.000881j
[2025-09-03 17:09:04] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -85.420537-0.004448j
[2025-09-03 17:09:04] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-09-03 17:09:35] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -85.662736-0.000123j
[2025-09-03 17:10:06] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -85.582944-0.001842j
[2025-09-03 17:10:37] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -85.702494+0.007205j
[2025-09-03 17:11:08] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -85.706189+0.005831j
[2025-09-03 17:11:39] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -85.666457+0.007535j
[2025-09-03 17:12:10] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -85.564867+0.003674j
[2025-09-03 17:12:41] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -85.741884-0.000167j
[2025-09-03 17:13:12] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -85.705623-0.005099j
[2025-09-03 17:13:43] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -85.768282-0.005168j
[2025-09-03 17:14:14] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -85.862088-0.000973j
[2025-09-03 17:14:45] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -85.810963+0.001457j
[2025-09-03 17:15:16] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -85.748847-0.000418j
[2025-09-03 17:15:47] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -85.676061-0.004729j
[2025-09-03 17:16:18] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -85.757147-0.001594j
[2025-09-03 17:16:49] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -85.708814-0.003156j
[2025-09-03 17:17:20] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -85.665260-0.002640j
[2025-09-03 17:17:52] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -85.757913-0.002281j
[2025-09-03 17:18:23] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -85.780717-0.003520j
[2025-09-03 17:18:54] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -85.832687+0.004610j
[2025-09-03 17:19:25] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -85.741370+0.003765j
[2025-09-03 17:19:56] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -85.603142+0.004647j
[2025-09-03 17:20:27] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -85.723451-0.002611j
[2025-09-03 17:20:58] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -85.759917+0.001893j
[2025-09-03 17:21:29] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -85.606471+0.005886j
[2025-09-03 17:22:00] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -85.742579+0.000686j
[2025-09-03 17:22:31] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -85.630940+0.002396j
[2025-09-03 17:23:02] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -85.720819-0.004651j
[2025-09-03 17:23:33] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -85.739014+0.000973j
[2025-09-03 17:24:04] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -85.855615+0.000906j
[2025-09-03 17:24:35] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -85.727449-0.004720j
[2025-09-03 17:25:06] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -85.734814+0.000737j
[2025-09-03 17:25:37] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -85.799201-0.002371j
[2025-09-03 17:26:08] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -85.736417-0.000629j
[2025-09-03 17:26:39] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -85.530491-0.003659j
[2025-09-03 17:27:10] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -85.491785-0.001674j
[2025-09-03 17:27:41] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -85.658623-0.002556j
[2025-09-03 17:28:12] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -85.628823+0.004859j
[2025-09-03 17:28:43] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -85.459519+0.003063j
[2025-09-03 17:29:14] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -85.679572-0.000261j
[2025-09-03 17:29:45] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -85.598501+0.000607j
[2025-09-03 17:30:16] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -85.754727-0.001334j
[2025-09-03 17:30:48] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -85.775134+0.005559j
[2025-09-03 17:31:19] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -85.672629+0.000131j
[2025-09-03 17:31:50] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -85.582149+0.001391j
[2025-09-03 17:32:21] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -85.609034-0.000206j
[2025-09-03 17:32:52] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -85.730624+0.006172j
[2025-09-03 17:33:23] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -85.694743+0.005739j
[2025-09-03 17:33:54] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -85.629748-0.002001j
[2025-09-03 17:34:25] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -85.570893-0.000616j
[2025-09-03 17:34:56] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -85.510746+0.000710j
[2025-09-03 17:34:56] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-09-03 17:35:27] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -85.359223-0.005895j
[2025-09-03 17:35:58] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -85.470900+0.001009j
[2025-09-03 17:36:29] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -85.583873+0.000445j
[2025-09-03 17:37:00] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -85.475112+0.001522j
[2025-09-03 17:37:31] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -85.566886-0.010173j
[2025-09-03 17:38:02] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -85.538648-0.006289j
[2025-09-03 17:38:33] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -85.851110+0.004829j
[2025-09-03 17:39:04] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -85.645632+0.005857j
[2025-09-03 17:39:35] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -85.703143-0.004146j
[2025-09-03 17:40:06] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -85.683255-0.001853j
[2025-09-03 17:40:37] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -85.691587+0.005657j
[2025-09-03 17:41:08] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -85.635340-0.000123j
[2025-09-03 17:41:39] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -85.714774-0.007294j
[2025-09-03 17:42:11] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -85.587734-0.002849j
[2025-09-03 17:42:42] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -85.467334-0.006234j
[2025-09-03 17:43:13] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -85.601310-0.000056j
[2025-09-03 17:43:44] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -85.603298-0.002095j
[2025-09-03 17:44:15] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -85.500124+0.000708j
[2025-09-03 17:44:46] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -85.536345-0.006384j
[2025-09-03 17:45:17] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -85.574524-0.004849j
[2025-09-03 17:45:48] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -85.495634+0.000355j
[2025-09-03 17:46:19] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -85.660276+0.002099j
[2025-09-03 17:46:50] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -85.447596-0.003806j
[2025-09-03 17:47:21] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -85.468626+0.003024j
[2025-09-03 17:47:52] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -85.567707+0.002591j
[2025-09-03 17:48:23] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -85.707315+0.003625j
[2025-09-03 17:48:54] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -85.604335-0.000573j
[2025-09-03 17:49:25] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -85.677677+0.002369j
[2025-09-03 17:49:56] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -85.593636-0.003828j
[2025-09-03 17:50:27] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -85.739610-0.001002j
[2025-09-03 17:50:59] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -85.688756-0.006991j
[2025-09-03 17:51:30] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -85.625752-0.001321j
[2025-09-03 17:52:01] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -85.692270+0.002633j
[2025-09-03 17:52:32] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -85.668793-0.000053j
[2025-09-03 17:53:03] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -85.613896+0.002354j
[2025-09-03 17:53:34] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -85.659322+0.000583j
[2025-09-03 17:54:05] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -85.434597+0.002181j
[2025-09-03 17:54:36] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -85.531995-0.000999j
[2025-09-03 17:55:07] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -85.586807+0.002815j
[2025-09-03 17:55:38] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -85.699227+0.002781j
[2025-09-03 17:56:09] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -85.514944-0.007124j
[2025-09-03 17:56:40] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -85.557901+0.001138j
[2025-09-03 17:57:11] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -85.595744-0.001480j
[2025-09-03 17:57:42] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -85.547977+0.000428j
[2025-09-03 17:58:13] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -85.584458-0.000738j
[2025-09-03 17:58:44] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -85.532480+0.003575j
[2025-09-03 17:59:15] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -85.561369-0.005350j
[2025-09-03 17:59:46] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -85.610546+0.003134j
[2025-09-03 18:00:17] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -85.525782-0.001373j
[2025-09-03 18:00:48] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -85.548965+0.001643j
[2025-09-03 18:00:48] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-09-03 18:01:19] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -85.609726-0.003811j
[2025-09-03 18:01:51] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -85.585071+0.003935j
[2025-09-03 18:02:22] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -85.580014-0.003693j
[2025-09-03 18:02:53] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -85.433602-0.004454j
[2025-09-03 18:03:24] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -85.537165-0.001877j
[2025-09-03 18:03:55] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -85.515510+0.000736j
[2025-09-03 18:04:26] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -85.472310-0.003330j
[2025-09-03 18:04:57] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -85.634579-0.004574j
[2025-09-03 18:05:28] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -85.563881+0.003474j
[2025-09-03 18:05:59] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -85.561135-0.001927j
[2025-09-03 18:06:30] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -85.689660+0.002762j
[2025-09-03 18:07:02] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -85.772482+0.000662j
[2025-09-03 18:07:33] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -85.975490+0.002880j
[2025-09-03 18:08:04] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -86.054014-0.001401j
[2025-09-03 18:08:35] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -85.798349+0.006212j
[2025-09-03 18:09:06] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -85.739685-0.006397j
[2025-09-03 18:09:37] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -85.661844+0.002640j
[2025-09-03 18:10:08] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -85.619743+0.003793j
[2025-09-03 18:10:39] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -85.785935+0.001095j
[2025-09-03 18:11:10] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -85.604837-0.001942j
[2025-09-03 18:11:41] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -85.607483-0.005536j
[2025-09-03 18:12:12] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -85.617031+0.000487j
[2025-09-03 18:12:43] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -85.596020-0.000593j
[2025-09-03 18:13:14] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -85.645922-0.001879j
[2025-09-03 18:13:45] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -85.578217-0.007966j
[2025-09-03 18:14:17] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -85.518614-0.002797j
[2025-09-03 18:14:48] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -85.621922+0.005049j
[2025-09-03 18:15:19] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -85.796324+0.003251j
[2025-09-03 18:15:50] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -85.652953+0.006173j
[2025-09-03 18:16:21] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -85.710962-0.003588j
[2025-09-03 18:16:52] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -85.655835-0.000940j
[2025-09-03 18:17:23] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -85.645377-0.000612j
[2025-09-03 18:17:54] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -85.618779-0.000918j
[2025-09-03 18:18:25] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -85.745694-0.005855j
[2025-09-03 18:18:56] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -85.741178+0.003719j
[2025-09-03 18:19:27] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -85.840727+0.000731j
[2025-09-03 18:19:58] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -85.735556+0.001691j
[2025-09-03 18:20:29] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -85.682862+0.002878j
[2025-09-03 18:21:01] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -85.562165-0.002723j
[2025-09-03 18:21:32] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -85.467120-0.004874j
[2025-09-03 18:22:03] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -85.565991-0.000014j
[2025-09-03 18:22:34] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -85.553545-0.004444j
[2025-09-03 18:23:05] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -85.635285-0.004362j
[2025-09-03 18:23:36] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -85.672931-0.003181j
[2025-09-03 18:24:07] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -85.721930+0.002529j
[2025-09-03 18:24:38] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -85.778935+0.003329j
[2025-09-03 18:25:09] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -85.761335-0.001257j
[2025-09-03 18:25:40] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -85.719765-0.001575j
[2025-09-03 18:26:11] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -85.644713-0.001743j
[2025-09-03 18:26:41] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -85.592229-0.002646j
[2025-09-03 18:26:41] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-09-03 18:26:41] ✅ Training completed | Restarts: 1
[2025-09-03 18:26:41] ============================================================
[2025-09-03 18:26:41] Training completed | Runtime: 13992.6s
[2025-09-03 18:26:49] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-03 18:26:49] ============================================================
