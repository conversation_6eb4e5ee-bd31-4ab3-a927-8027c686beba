[2025-09-03 18:27:01] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-09-03 18:27:01]   - 迭代次数: final
[2025-09-03 18:27:01]   - 能量: -85.592229-0.002646j ± 0.114667
[2025-09-03 18:27:01]   - 时间戳: 2025-09-03T18:26:49.945498+08:00
[2025-09-03 18:27:14] ✓ 变分状态参数已从checkpoint恢复
[2025-09-03 18:27:14] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-03 18:27:14] ==================================================
[2025-09-03 18:27:14] GCNN for Shastry-Sutherland Model
[2025-09-03 18:27:14] ==================================================
[2025-09-03 18:27:14] System parameters:
[2025-09-03 18:27:14]   - System size: L=5, N=100
[2025-09-03 18:27:14]   - System parameters: J1=0.06, J2=0.0, Q=1.0
[2025-09-03 18:27:14] --------------------------------------------------
[2025-09-03 18:27:14] Model parameters:
[2025-09-03 18:27:14]   - Number of layers = 4
[2025-09-03 18:27:14]   - Number of features = 4
[2025-09-03 18:27:14]   - Total parameters = 19628
[2025-09-03 18:27:14] --------------------------------------------------
[2025-09-03 18:27:14] Training parameters:
[2025-09-03 18:27:14]   - Learning rate: 0.015
[2025-09-03 18:27:14]   - Total iterations: 450
[2025-09-03 18:27:14]   - Annealing cycles: 2
[2025-09-03 18:27:14]   - Initial period: 150
[2025-09-03 18:27:14]   - Period multiplier: 2.0
[2025-09-03 18:27:14]   - Temperature range: 0.0-1.0
[2025-09-03 18:27:14]   - Samples: 4096
[2025-09-03 18:27:14]   - Discarded samples: 0
[2025-09-03 18:27:14]   - Chunk size: 2048
[2025-09-03 18:27:14]   - Diagonal shift: 0.2
[2025-09-03 18:27:14]   - Gradient clipping: 1.0
[2025-09-03 18:27:14]   - Checkpoint enabled: interval=50
[2025-09-03 18:27:14]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.06/training/checkpoints
[2025-09-03 18:27:14] --------------------------------------------------
[2025-09-03 18:27:14] Device status:
[2025-09-03 18:27:14]   - Devices model: NVIDIA H200 NVL
[2025-09-03 18:27:14]   - Number of devices: 1
[2025-09-03 18:27:14]   - Sharding: True
[2025-09-03 18:27:14] ============================================================
[2025-09-03 18:28:14] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -86.001214+0.016838j
[2025-09-03 18:29:02] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -86.164918+0.001954j
[2025-09-03 18:29:33] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -86.320226-0.000209j
[2025-09-03 18:30:04] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -86.188400-0.008047j
[2025-09-03 18:30:35] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -86.140643+0.006233j
[2025-09-03 18:31:05] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -86.128735-0.003051j
[2025-09-03 18:31:36] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -86.220761-0.004836j
[2025-09-03 18:32:07] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -86.508042-0.005490j
[2025-09-03 18:32:38] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -86.349170-0.011613j
[2025-09-03 18:33:09] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -86.502749-0.000398j
[2025-09-03 18:33:40] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -86.436179-0.000707j
[2025-09-03 18:34:11] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -86.295645-0.004599j
[2025-09-03 18:34:42] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -86.199385+0.001277j
[2025-09-03 18:35:13] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -86.251415-0.000147j
[2025-09-03 18:35:44] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -86.166916-0.004542j
[2025-09-03 18:36:15] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -86.149405-0.006980j
[2025-09-03 18:36:46] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -86.255964-0.007190j
[2025-09-03 18:37:17] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -86.048473-0.009246j
[2025-09-03 18:37:48] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -86.031316-0.000850j
[2025-09-03 18:38:19] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -86.007061+0.003207j
[2025-09-03 18:38:50] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -86.122070-0.006088j
[2025-09-03 18:39:21] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -86.194143-0.010602j
[2025-09-03 18:39:52] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -86.372902-0.004775j
[2025-09-03 18:40:23] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -86.183686-0.002824j
[2025-09-03 18:40:54] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -86.273107-0.002441j
[2025-09-03 18:41:25] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -86.208456-0.001260j
[2025-09-03 18:41:56] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -86.389591+0.003255j
[2025-09-03 18:42:27] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -86.458754-0.004979j
[2025-09-03 18:42:58] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -86.390029-0.008609j
[2025-09-03 18:43:29] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -86.258753+0.002335j
[2025-09-03 18:44:00] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -86.235326-0.002135j
[2025-09-03 18:44:31] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -86.382293+0.001441j
[2025-09-03 18:45:02] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -86.236891-0.000590j
[2025-09-03 18:45:32] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -86.436434-0.002272j
[2025-09-03 18:46:03] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -86.370206+0.008155j
[2025-09-03 18:46:34] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -86.367992-0.000159j
[2025-09-03 18:47:05] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -86.396083-0.012897j
[2025-09-03 18:47:36] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -86.063609-0.000827j
[2025-09-03 18:48:07] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -86.008016-0.003606j
[2025-09-03 18:48:38] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -86.133173+0.000429j
[2025-09-03 18:49:09] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -86.116754-0.000222j
[2025-09-03 18:49:40] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -86.238160-0.002927j
[2025-09-03 18:50:11] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -86.288923+0.002019j
[2025-09-03 18:50:42] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -86.286876+0.001150j
[2025-09-03 18:51:13] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -86.218751+0.002837j
[2025-09-03 18:51:44] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -86.289283+0.004177j
[2025-09-03 18:52:15] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -86.301713-0.000119j
[2025-09-03 18:52:46] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -86.346594-0.002372j
[2025-09-03 18:53:17] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -86.285727+0.008007j
[2025-09-03 18:53:48] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -86.129107+0.006896j
[2025-09-03 18:53:49] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-09-03 18:54:20] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -86.174146+0.000552j
[2025-09-03 18:54:51] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -86.162613+0.004461j
[2025-09-03 18:55:22] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -86.153617+0.002848j
[2025-09-03 18:55:53] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -86.187562+0.000300j
[2025-09-03 18:56:24] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -86.076813-0.002130j
[2025-09-03 18:56:55] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -86.137500+0.000610j
[2025-09-03 18:57:26] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -86.189284+0.003102j
[2025-09-03 18:57:57] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -86.131465+0.002983j
[2025-09-03 18:58:28] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -86.106194+0.004657j
[2025-09-03 18:58:59] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -86.168765+0.001464j
[2025-09-03 18:59:30] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -86.299634-0.002692j
[2025-09-03 19:00:00] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -86.095659+0.003110j
[2025-09-03 19:00:31] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -86.259293+0.003945j
[2025-09-03 19:01:02] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -86.361110+0.005939j
[2025-09-03 19:01:33] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -86.318690+0.008033j
[2025-09-03 19:02:04] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -86.259166-0.006981j
[2025-09-03 19:02:35] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -86.280521-0.012786j
[2025-09-03 19:03:06] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -86.252860+0.001071j
[2025-09-03 19:03:37] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -86.186290-0.004355j
[2025-09-03 19:04:08] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -86.099960+0.006894j
[2025-09-03 19:04:39] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -86.160702-0.001863j
[2025-09-03 19:05:10] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -86.131728-0.000108j
[2025-09-03 19:05:41] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -86.181280-0.003044j
[2025-09-03 19:06:12] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -86.249006+0.002968j
[2025-09-03 19:06:43] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -86.181632-0.000706j
[2025-09-03 19:07:14] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -86.344195-0.003298j
[2025-09-03 19:07:45] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -86.255043-0.007666j
[2025-09-03 19:08:16] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -86.191189+0.003931j
[2025-09-03 19:08:47] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -86.287424-0.000409j
[2025-09-03 19:09:18] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -86.349508+0.003154j
[2025-09-03 19:09:49] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -86.300273-0.003055j
[2025-09-03 19:10:20] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -86.268981+0.000167j
[2025-09-03 19:10:51] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -86.181559+0.002135j
[2025-09-03 19:11:22] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -86.238159+0.002291j
[2025-09-03 19:11:53] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -86.146392+0.001935j
[2025-09-03 19:12:24] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -86.139106+0.009326j
[2025-09-03 19:12:55] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -86.335548+0.003014j
[2025-09-03 19:13:26] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -86.315696+0.004083j
[2025-09-03 19:13:57] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -86.356362+0.001935j
[2025-09-03 19:14:28] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -86.065417+0.003100j
[2025-09-03 19:14:59] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -86.127718-0.004582j
[2025-09-03 19:15:30] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -86.191906+0.006040j
[2025-09-03 19:16:01] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -86.291941-0.006971j
[2025-09-03 19:16:32] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -86.363097+0.003882j
[2025-09-03 19:17:03] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -86.507325-0.004229j
[2025-09-03 19:17:34] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -86.245940-0.009258j
[2025-09-03 19:18:05] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -86.234658-0.001886j
[2025-09-03 19:18:36] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -86.118305-0.000745j
[2025-09-03 19:19:07] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -86.162233+0.003971j
[2025-09-03 19:19:38] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -86.267645+0.002154j
[2025-09-03 19:19:38] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-09-03 19:20:09] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -86.014951+0.003866j
[2025-09-03 19:20:40] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -86.140742+0.001924j
[2025-09-03 19:21:11] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -86.113869+0.006920j
[2025-09-03 19:21:42] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -86.276625+0.000957j
[2025-09-03 19:22:13] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -86.159400+0.001037j
[2025-09-03 19:22:44] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -86.169434+0.002243j
[2025-09-03 19:23:15] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -86.167558+0.009282j
[2025-09-03 19:23:46] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -86.200952+0.004409j
[2025-09-03 19:24:17] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -86.207988-0.000717j
[2025-09-03 19:24:48] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -86.274251+0.000213j
[2025-09-03 19:25:19] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -86.233597+0.000080j
[2025-09-03 19:25:50] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -86.252904+0.004466j
[2025-09-03 19:26:21] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -86.261026-0.002147j
[2025-09-03 19:26:52] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -86.227793+0.008242j
[2025-09-03 19:27:23] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -86.185150-0.003344j
[2025-09-03 19:27:54] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -86.355701-0.001920j
[2025-09-03 19:28:25] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -86.307173-0.003050j
[2025-09-03 19:28:56] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -86.346458+0.003907j
[2025-09-03 19:29:27] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -86.233369-0.003468j
[2025-09-03 19:29:57] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -86.297009-0.004090j
[2025-09-03 19:30:28] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -86.334036+0.003125j
[2025-09-03 19:30:59] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -86.389676+0.000534j
[2025-09-03 19:31:30] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -86.288912+0.006443j
[2025-09-03 19:32:01] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -86.298322-0.001384j
[2025-09-03 19:32:32] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -86.240659-0.003250j
[2025-09-03 19:33:03] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -86.254392+0.001230j
[2025-09-03 19:33:34] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -86.191988+0.006932j
[2025-09-03 19:34:05] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -86.270137-0.005425j
[2025-09-03 19:34:36] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -86.231287+0.004078j
[2025-09-03 19:35:07] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -86.309846+0.002869j
[2025-09-03 19:35:38] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -86.276543-0.005385j
[2025-09-03 19:36:09] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -86.249188+0.005227j
[2025-09-03 19:36:40] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -86.214114+0.003379j
[2025-09-03 19:37:11] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -86.413415-0.010556j
[2025-09-03 19:37:42] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -86.378314-0.001285j
[2025-09-03 19:38:13] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -86.295664-0.002949j
[2025-09-03 19:38:44] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -86.257236+0.004868j
[2025-09-03 19:39:15] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -86.339789-0.003666j
[2025-09-03 19:39:46] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -86.290030+0.001490j
[2025-09-03 19:40:17] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -86.296069+0.000660j
[2025-09-03 19:40:48] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -86.210286+0.002621j
[2025-09-03 19:41:19] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -86.396803-0.000529j
[2025-09-03 19:41:50] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -86.392937+0.001745j
[2025-09-03 19:42:21] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -86.373884-0.001435j
[2025-09-03 19:42:51] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -86.325001+0.000389j
[2025-09-03 19:43:22] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -86.312900-0.000795j
[2025-09-03 19:43:53] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -86.229349+0.002760j
[2025-09-03 19:44:24] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -86.305848+0.000708j
[2025-09-03 19:44:55] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -86.186328-0.002662j
[2025-09-03 19:45:26] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -86.118097-0.002668j
[2025-09-03 19:45:26] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-09-03 19:45:26] RESTART #1 | Period: 300
[2025-09-03 19:45:57] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -86.099834-0.006162j
[2025-09-03 19:46:28] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -86.100723-0.001119j
[2025-09-03 19:46:59] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -86.075050-0.008431j
[2025-09-03 19:47:30] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -86.162090-0.003450j
[2025-09-03 19:48:01] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -86.202101-0.001953j
[2025-09-03 19:48:32] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -86.306005+0.005525j
[2025-09-03 19:49:03] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -86.153058+0.017290j
[2025-09-03 19:49:34] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -86.339028-0.000209j
[2025-09-03 19:50:05] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -86.249972-0.005174j
[2025-09-03 19:50:36] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -86.290007+0.000573j
[2025-09-03 19:51:07] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -86.218616-0.000447j
[2025-09-03 19:51:38] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -86.251218+0.002804j
[2025-09-03 19:52:09] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -86.095806-0.000905j
[2025-09-03 19:52:40] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -86.123684-0.002514j
[2025-09-03 19:53:11] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -86.123710-0.002217j
[2025-09-03 19:53:42] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -85.986693+0.001254j
[2025-09-03 19:54:13] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -86.085958+0.002113j
[2025-09-03 19:54:44] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -86.296503-0.003723j
[2025-09-03 19:55:15] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -86.233981-0.000152j
[2025-09-03 19:55:46] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -86.195451-0.004753j
[2025-09-03 19:56:17] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -86.178813+0.000259j
[2025-09-03 19:56:48] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -86.158119-0.001882j
[2025-09-03 19:57:19] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -86.237723-0.000209j
[2025-09-03 19:57:50] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -86.412101+0.000461j
[2025-09-03 19:58:21] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -86.309739+0.000129j
[2025-09-03 19:58:52] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -86.163274+0.004020j
[2025-09-03 19:59:23] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -86.202658+0.002002j
[2025-09-03 19:59:54] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -86.424591+0.000834j
[2025-09-03 20:00:25] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -86.429302-0.000123j
[2025-09-03 20:00:56] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -86.490949+0.000762j
[2025-09-03 20:01:27] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -86.301764+0.000967j
[2025-09-03 20:01:58] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -86.324990-0.002596j
[2025-09-03 20:02:29] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -86.262581-0.002393j
[2025-09-03 20:03:00] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -86.031821-0.000043j
[2025-09-03 20:03:31] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -86.162894-0.004247j
[2025-09-03 20:04:02] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -86.105546+0.003151j
[2025-09-03 20:04:33] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -86.139200-0.002591j
[2025-09-03 20:05:04] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -86.309328-0.001930j
[2025-09-03 20:05:35] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -86.299417+0.008625j
[2025-09-03 20:06:06] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -86.231335+0.004008j
[2025-09-03 20:06:37] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -86.060219+0.000927j
[2025-09-03 20:07:08] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -86.210749-0.006005j
[2025-09-03 20:07:39] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -86.130882-0.006799j
[2025-09-03 20:08:10] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -86.211452+0.002162j
[2025-09-03 20:08:40] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -86.268905-0.005916j
[2025-09-03 20:09:11] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -86.384760+0.002004j
[2025-09-03 20:09:42] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -86.434957-0.010783j
[2025-09-03 20:10:13] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -86.331854+0.001913j
[2025-09-03 20:10:44] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -86.227595-0.006633j
[2025-09-03 20:11:15] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -86.273838+0.005269j
[2025-09-03 20:11:15] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-09-03 20:11:46] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -86.236658-0.001765j
[2025-09-03 20:12:17] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -86.291134+0.000502j
[2025-09-03 20:12:48] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -86.414914-0.000181j
[2025-09-03 20:13:19] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -86.483865-0.004381j
[2025-09-03 20:13:50] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -86.481827-0.002424j
[2025-09-03 20:14:21] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -86.381759-0.001057j
[2025-09-03 20:14:52] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -86.210242-0.002360j
[2025-09-03 20:15:23] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -86.172611-0.003323j
[2025-09-03 20:15:54] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -86.323809-0.004117j
[2025-09-03 20:16:25] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -86.429847-0.000723j
[2025-09-03 20:16:56] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -86.340961+0.000032j
[2025-09-03 20:17:27] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -86.237030-0.001984j
[2025-09-03 20:17:58] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -86.373214-0.002801j
[2025-09-03 20:18:29] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -86.312715-0.001524j
[2025-09-03 20:19:00] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -86.319995+0.004094j
[2025-09-03 20:19:31] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -86.130182+0.002298j
[2025-09-03 20:20:02] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -86.214742+0.004051j
[2025-09-03 20:20:33] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -86.180439-0.002082j
[2025-09-03 20:21:04] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -86.442214-0.003993j
[2025-09-03 20:21:35] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -86.342499-0.000645j
[2025-09-03 20:22:06] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -86.319545-0.003179j
[2025-09-03 20:22:37] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -86.283471-0.004232j
[2025-09-03 20:23:08] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -86.245797+0.001691j
[2025-09-03 20:23:39] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -86.274095+0.000883j
[2025-09-03 20:24:10] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -86.170689+0.003252j
[2025-09-03 20:24:41] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -86.142716-0.005415j
[2025-09-03 20:25:12] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -86.368028-0.003871j
[2025-09-03 20:25:43] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -86.168901+0.000767j
[2025-09-03 20:26:14] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -86.107895+0.001249j
[2025-09-03 20:26:45] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -86.220600+0.000731j
[2025-09-03 20:27:16] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -86.172076-0.001901j
[2025-09-03 20:27:47] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -86.128517-0.001673j
[2025-09-03 20:28:18] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -86.291310-0.000665j
[2025-09-03 20:28:49] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -86.278918+0.017177j
[2025-09-03 20:29:20] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -86.361701+0.003806j
[2025-09-03 20:29:50] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -86.395255-0.007500j
[2025-09-03 20:30:21] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -86.164044-0.000441j
[2025-09-03 20:30:52] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -86.200458+0.002928j
[2025-09-03 20:31:23] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -86.227021+0.007784j
[2025-09-03 20:31:54] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -86.267033+0.002537j
[2025-09-03 20:32:25] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -86.471420+0.000165j
[2025-09-03 20:32:56] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -86.544604-0.012770j
[2025-09-03 20:33:27] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -86.368448-0.004172j
[2025-09-03 20:33:58] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -86.254405-0.003469j
[2025-09-03 20:34:29] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -86.426665-0.000757j
[2025-09-03 20:35:00] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -86.301688-0.000349j
[2025-09-03 20:35:31] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -86.393186+0.001331j
[2025-09-03 20:36:02] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -86.383378-0.001344j
[2025-09-03 20:36:33] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -86.087564-0.005433j
[2025-09-03 20:37:04] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -86.215579-0.001445j
[2025-09-03 20:37:04] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-03 20:37:35] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -86.402922-0.002261j
[2025-09-03 20:38:06] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -86.100957+0.000736j
[2025-09-03 20:38:37] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -86.198900+0.003367j
[2025-09-03 20:39:08] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -86.173304-0.000423j
[2025-09-03 20:39:39] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -86.095573-0.005668j
[2025-09-03 20:40:10] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -86.251303+0.000417j
[2025-09-03 20:40:41] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -86.351880+0.008980j
[2025-09-03 20:41:12] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -86.264424-0.002996j
[2025-09-03 20:41:43] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -86.281927+0.000988j
[2025-09-03 20:42:14] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -86.251841-0.000575j
[2025-09-03 20:42:45] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -86.210453+0.000080j
[2025-09-03 20:43:16] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -86.098558-0.001030j
[2025-09-03 20:43:47] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -86.113697-0.004937j
[2025-09-03 20:44:18] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -86.138496-0.000085j
[2025-09-03 20:44:48] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -86.278203+0.006805j
[2025-09-03 20:45:20] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -86.227097+0.001564j
[2025-09-03 20:45:51] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -86.162621+0.003923j
[2025-09-03 20:46:22] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -86.140661+0.002759j
[2025-09-03 20:46:53] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -86.232131+0.004735j
[2025-09-03 20:47:24] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -86.113494+0.007437j
[2025-09-03 20:47:55] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -86.173027-0.005305j
[2025-09-03 20:48:26] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -86.359630-0.004230j
[2025-09-03 20:48:57] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -86.338489+0.000230j
[2025-09-03 20:49:28] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -86.253177+0.000882j
[2025-09-03 20:49:59] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -86.287397-0.006093j
[2025-09-03 20:50:30] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -86.175410+0.003062j
[2025-09-03 20:51:01] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -86.269565-0.001129j
[2025-09-03 20:51:32] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -86.329215+0.000685j
[2025-09-03 20:52:03] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -86.236509-0.001163j
[2025-09-03 20:52:34] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -86.298064-0.004429j
[2025-09-03 20:53:05] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -86.236222+0.001283j
[2025-09-03 20:53:36] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -86.276375+0.006597j
[2025-09-03 20:54:07] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -86.420524-0.005233j
[2025-09-03 20:54:38] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -86.225678-0.003175j
[2025-09-03 20:55:08] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -86.277936+0.003691j
[2025-09-03 20:55:39] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -86.355361-0.001943j
[2025-09-03 20:56:10] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -86.408565-0.002588j
[2025-09-03 20:56:41] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -86.319488-0.000762j
[2025-09-03 20:57:12] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -86.346978-0.004451j
[2025-09-03 20:57:43] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -86.477832-0.001114j
[2025-09-03 20:58:14] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -86.419916+0.005700j
[2025-09-03 20:58:45] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -86.454947+0.000933j
[2025-09-03 20:59:16] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -86.324061-0.002086j
[2025-09-03 20:59:47] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -86.226156-0.002776j
[2025-09-03 21:00:18] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -86.242493-0.002250j
[2025-09-03 21:00:49] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -86.371534-0.002196j
[2025-09-03 21:01:20] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -86.335339+0.005301j
[2025-09-03 21:01:51] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -86.131698+0.003147j
[2025-09-03 21:02:22] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -86.205104+0.002765j
[2025-09-03 21:02:53] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -86.163309-0.003058j
[2025-09-03 21:02:53] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-09-03 21:03:24] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -86.297997-0.003722j
[2025-09-03 21:03:55] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -86.323923-0.007585j
[2025-09-03 21:04:26] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -86.285681-0.007652j
[2025-09-03 21:04:57] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -86.187878-0.003101j
[2025-09-03 21:05:27] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -86.188343-0.000104j
[2025-09-03 21:05:58] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -86.255480+0.002004j
[2025-09-03 21:06:29] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -86.291026-0.007704j
[2025-09-03 21:07:00] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -86.376436+0.002317j
[2025-09-03 21:07:31] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -86.200560+0.000126j
[2025-09-03 21:08:02] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -86.181667-0.006224j
[2025-09-03 21:08:33] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -86.245532-0.002175j
[2025-09-03 21:09:04] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -86.147032-0.001487j
[2025-09-03 21:09:35] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -86.230715-0.004548j
[2025-09-03 21:10:06] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -86.292835-0.000004j
[2025-09-03 21:10:37] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -86.463158+0.006831j
[2025-09-03 21:11:08] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -86.419222+0.005393j
[2025-09-03 21:11:39] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -86.239158+0.001864j
[2025-09-03 21:12:10] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -86.123770-0.003286j
[2025-09-03 21:12:41] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -86.106432+0.002187j
[2025-09-03 21:13:12] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -86.056681+0.003738j
[2025-09-03 21:13:42] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -86.120376+0.000183j
[2025-09-03 21:14:13] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -86.035331+0.000977j
[2025-09-03 21:14:44] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -85.985709+0.003330j
[2025-09-03 21:15:16] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -86.236347+0.004155j
[2025-09-03 21:15:47] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -86.225221+0.002040j
[2025-09-03 21:16:18] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -86.311430+0.001668j
[2025-09-03 21:16:49] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -86.406995+0.001615j
[2025-09-03 21:17:20] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -86.205701+0.000342j
[2025-09-03 21:17:51] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -86.329955-0.002224j
[2025-09-03 21:18:22] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -86.412235+0.004630j
[2025-09-03 21:18:53] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -86.389263-0.001624j
[2025-09-03 21:19:24] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -86.294274-0.001480j
[2025-09-03 21:19:55] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -86.326275-0.001398j
[2025-09-03 21:20:26] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -86.381262+0.006527j
[2025-09-03 21:20:57] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -86.243077-0.007060j
[2025-09-03 21:21:27] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -86.276593+0.002657j
[2025-09-03 21:21:58] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -86.256810+0.000753j
[2025-09-03 21:22:29] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -86.355681+0.004686j
[2025-09-03 21:23:00] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -86.390217+0.004441j
[2025-09-03 21:23:31] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -86.492675-0.001180j
[2025-09-03 21:24:02] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -86.543625-0.003239j
[2025-09-03 21:24:33] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -86.520717+0.004726j
[2025-09-03 21:25:05] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -86.427737-0.000186j
[2025-09-03 21:25:36] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -86.283730+0.000344j
[2025-09-03 21:26:07] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -86.312713-0.003089j
[2025-09-03 21:26:38] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -86.433974+0.002164j
[2025-09-03 21:27:09] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -86.412352+0.008416j
[2025-09-03 21:27:40] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -86.321995-0.001870j
[2025-09-03 21:28:11] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -86.164054-0.001612j
[2025-09-03 21:28:42] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -86.125387+0.004067j
[2025-09-03 21:28:42] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-09-03 21:29:13] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -86.143925+0.000091j
[2025-09-03 21:29:44] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -86.308918+0.002580j
[2025-09-03 21:30:15] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -86.119489+0.000998j
[2025-09-03 21:30:46] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -86.149085+0.004252j
[2025-09-03 21:31:17] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -86.101138+0.005510j
[2025-09-03 21:31:48] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -86.095176-0.006695j
[2025-09-03 21:32:19] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -86.031429-0.000375j
[2025-09-03 21:32:50] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -86.246448-0.003940j
[2025-09-03 21:33:21] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -86.259097-0.000831j
[2025-09-03 21:33:52] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -86.149132+0.002924j
[2025-09-03 21:34:23] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -86.213119-0.003107j
[2025-09-03 21:34:54] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -86.374078-0.000921j
[2025-09-03 21:35:25] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -85.965276+0.006203j
[2025-09-03 21:35:56] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -86.261351+0.002797j
[2025-09-03 21:36:27] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -86.282476-0.002236j
[2025-09-03 21:36:58] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -86.286320+0.000607j
[2025-09-03 21:37:29] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -86.213466+0.000374j
[2025-09-03 21:38:00] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -86.237731+0.007286j
[2025-09-03 21:38:31] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -86.231775-0.001472j
[2025-09-03 21:39:02] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -86.212018+0.000629j
[2025-09-03 21:39:33] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -86.279069-0.003547j
[2025-09-03 21:40:04] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -86.217040+0.000078j
[2025-09-03 21:40:35] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -86.226119+0.002499j
[2025-09-03 21:41:06] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -86.378631+0.005834j
[2025-09-03 21:41:37] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -86.259220+0.000652j
[2025-09-03 21:42:08] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -86.194654+0.000298j
[2025-09-03 21:42:39] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -86.270163+0.002906j
[2025-09-03 21:43:10] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -86.205392+0.013180j
[2025-09-03 21:43:41] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -86.258196+0.000577j
[2025-09-03 21:44:12] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -86.341355-0.002287j
[2025-09-03 21:44:43] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -86.224997+0.001200j
[2025-09-03 21:45:14] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -86.194543+0.002162j
[2025-09-03 21:45:45] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -86.142108+0.002152j
[2025-09-03 21:46:16] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -86.112713-0.005018j
[2025-09-03 21:46:47] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -86.189145+0.000548j
[2025-09-03 21:47:18] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -86.129901+0.001868j
[2025-09-03 21:47:49] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -86.270844-0.000146j
[2025-09-03 21:48:19] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -86.215326-0.000011j
[2025-09-03 21:48:50] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -86.409313-0.005197j
[2025-09-03 21:49:21] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -86.309367-0.009941j
[2025-09-03 21:49:52] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -86.202780+0.002245j
[2025-09-03 21:50:23] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -86.240772-0.001874j
[2025-09-03 21:50:54] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -86.290281+0.000598j
[2025-09-03 21:51:25] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -86.219586+0.003645j
[2025-09-03 21:51:56] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -86.348084+0.000879j
[2025-09-03 21:52:27] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -86.189333+0.003515j
[2025-09-03 21:52:58] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -86.272012+0.001039j
[2025-09-03 21:53:29] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -86.545701-0.000662j
[2025-09-03 21:54:00] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -86.522280+0.001830j
[2025-09-03 21:54:31] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -86.373056-0.005695j
[2025-09-03 21:54:31] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-09-03 21:55:02] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -86.360264-0.005454j
[2025-09-03 21:55:33] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -86.396441+0.005742j
[2025-09-03 21:56:04] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -86.457069+0.002235j
[2025-09-03 21:56:35] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -86.515800+0.000540j
[2025-09-03 21:57:06] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -86.423011-0.001139j
[2025-09-03 21:57:37] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -86.329907-0.001257j
[2025-09-03 21:58:08] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -86.262767+0.000127j
[2025-09-03 21:58:39] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -86.263793+0.007268j
[2025-09-03 21:59:10] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -86.074051-0.000566j
[2025-09-03 21:59:40] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -86.310355+0.000431j
[2025-09-03 22:00:11] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -86.284189-0.005507j
[2025-09-03 22:00:42] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -86.391881+0.004443j
[2025-09-03 22:01:13] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -86.363200+0.003611j
[2025-09-03 22:01:44] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -86.346723+0.005861j
[2025-09-03 22:02:15] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -86.441180+0.001362j
[2025-09-03 22:02:46] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -86.168693+0.005716j
[2025-09-03 22:03:17] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -86.327820-0.000998j
[2025-09-03 22:03:48] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -86.320730+0.001634j
[2025-09-03 22:04:19] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -86.200722+0.006894j
[2025-09-03 22:04:50] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -86.299415+0.002003j
[2025-09-03 22:05:21] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -86.182704+0.006505j
[2025-09-03 22:05:52] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -86.338414-0.002182j
[2025-09-03 22:06:23] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -86.385906-0.008006j
[2025-09-03 22:06:54] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -86.348109+0.000233j
[2025-09-03 22:07:24] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -86.311627+0.003063j
[2025-09-03 22:07:56] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -86.347457-0.000067j
[2025-09-03 22:08:27] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -86.438881-0.004600j
[2025-09-03 22:08:58] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -86.317907+0.001800j
[2025-09-03 22:09:29] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -86.366010-0.001719j
[2025-09-03 22:10:00] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -86.183564-0.001628j
[2025-09-03 22:10:31] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -86.319577-0.001082j
[2025-09-03 22:11:02] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -86.292104+0.000171j
[2025-09-03 22:11:32] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -86.086337-0.001504j
[2025-09-03 22:12:03] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -86.300416-0.001461j
[2025-09-03 22:12:34] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -86.293231-0.001872j
[2025-09-03 22:13:05] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -86.298670+0.003574j
[2025-09-03 22:13:36] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -86.324184+0.001978j
[2025-09-03 22:14:07] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -86.175624-0.001116j
[2025-09-03 22:14:38] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -86.095633-0.000148j
[2025-09-03 22:15:09] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -86.171095+0.002017j
[2025-09-03 22:15:40] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -86.344792+0.001793j
[2025-09-03 22:16:11] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -86.280613-0.000607j
[2025-09-03 22:16:42] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -86.443745-0.006114j
[2025-09-03 22:17:13] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -86.432003-0.000747j
[2025-09-03 22:17:44] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -86.341146+0.004174j
[2025-09-03 22:18:16] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -86.240459-0.002032j
[2025-09-03 22:18:39] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -86.229827-0.004942j
[2025-09-03 22:19:00] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -86.134209-0.001306j
[2025-09-03 22:19:20] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -86.254047-0.000332j
[2025-09-03 22:19:41] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -86.303293+0.000835j
[2025-09-03 22:19:41] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-09-03 22:19:41] ✅ Training completed | Restarts: 1
[2025-09-03 22:19:41] ============================================================
[2025-09-03 22:19:41] Training completed | Runtime: 13947.6s
[2025-09-03 22:19:50] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-03 22:19:50] ============================================================
