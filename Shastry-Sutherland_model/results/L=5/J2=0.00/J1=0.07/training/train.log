[2025-09-03 22:20:02] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.06/training/checkpoints/final_GCNN.pkl
[2025-09-03 22:20:02]   - 迭代次数: final
[2025-09-03 22:20:02]   - 能量: -86.303293+0.000835j ± 0.112726
[2025-09-03 22:20:02]   - 时间戳: 2025-09-03T22:19:50.239027+08:00
[2025-09-03 22:20:16] ✓ 变分状态参数已从checkpoint恢复
[2025-09-03 22:20:16] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-03 22:20:16] ==================================================
[2025-09-03 22:20:16] GCNN for Shastry-Sutherland Model
[2025-09-03 22:20:16] ==================================================
[2025-09-03 22:20:16] System parameters:
[2025-09-03 22:20:16]   - System size: L=5, N=100
[2025-09-03 22:20:16]   - System parameters: J1=0.07, J2=0.0, Q=1.0
[2025-09-03 22:20:16] --------------------------------------------------
[2025-09-03 22:20:16] Model parameters:
[2025-09-03 22:20:16]   - Number of layers = 4
[2025-09-03 22:20:16]   - Number of features = 4
[2025-09-03 22:20:16]   - Total parameters = 19628
[2025-09-03 22:20:16] --------------------------------------------------
[2025-09-03 22:20:16] Training parameters:
[2025-09-03 22:20:16]   - Learning rate: 0.015
[2025-09-03 22:20:16]   - Total iterations: 450
[2025-09-03 22:20:16]   - Annealing cycles: 2
[2025-09-03 22:20:16]   - Initial period: 150
[2025-09-03 22:20:16]   - Period multiplier: 2.0
[2025-09-03 22:20:16]   - Temperature range: 0.0-1.0
[2025-09-03 22:20:16]   - Samples: 4096
[2025-09-03 22:20:16]   - Discarded samples: 0
[2025-09-03 22:20:16]   - Chunk size: 2048
[2025-09-03 22:20:16]   - Diagonal shift: 0.2
[2025-09-03 22:20:16]   - Gradient clipping: 1.0
[2025-09-03 22:20:16]   - Checkpoint enabled: interval=50
[2025-09-03 22:20:16]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.07/training/checkpoints
[2025-09-03 22:20:16] --------------------------------------------------
[2025-09-03 22:20:16] Device status:
[2025-09-03 22:20:16]   - Devices model: NVIDIA H200 NVL
[2025-09-03 22:20:16]   - Number of devices: 1
[2025-09-03 22:20:16]   - Sharding: True
[2025-09-03 22:20:16] ============================================================
[2025-09-03 22:21:17] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -86.964704-0.011821j
[2025-09-03 22:22:06] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -86.947160+0.000047j
[2025-09-03 22:22:37] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -86.970839-0.002344j
[2025-09-03 22:23:08] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -86.982242-0.000157j
[2025-09-03 22:23:39] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -87.141406-0.001765j
[2025-09-03 22:24:11] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -86.945870-0.002514j
[2025-09-03 22:24:42] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -86.840729-0.011586j
[2025-09-03 22:25:13] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -86.798010-0.001611j
[2025-09-03 22:25:44] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -86.895824+0.005583j
[2025-09-03 22:26:15] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -86.712380-0.000290j
[2025-09-03 22:26:46] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -86.892107+0.003055j
[2025-09-03 22:27:17] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -86.877473-0.007301j
[2025-09-03 22:27:48] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -86.815331-0.001479j
[2025-09-03 22:28:19] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -86.882004-0.003569j
[2025-09-03 22:28:50] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -86.853256-0.005708j
[2025-09-03 22:29:21] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -86.877084+0.000084j
[2025-09-03 22:29:52] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -86.843018+0.000205j
[2025-09-03 22:30:24] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -87.018184-0.003431j
[2025-09-03 22:30:55] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -86.893147+0.002650j
[2025-09-03 22:31:26] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -86.862411+0.000579j
[2025-09-03 22:31:57] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -86.922734-0.005356j
[2025-09-03 22:32:28] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -87.014856+0.000001j
[2025-09-03 22:32:59] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -86.844532+0.001423j
[2025-09-03 22:33:30] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -86.958010-0.006219j
[2025-09-03 22:34:01] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -86.975237+0.001192j
[2025-09-03 22:34:32] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -87.005707-0.005797j
[2025-09-03 22:35:03] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -86.900183+0.003089j
[2025-09-03 22:35:34] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -87.018243-0.000848j
[2025-09-03 22:36:05] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -86.961225+0.003444j
[2025-09-03 22:36:36] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -87.093351+0.001440j
[2025-09-03 22:37:07] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -86.897168+0.001145j
[2025-09-03 22:37:38] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -86.920274-0.002949j
[2025-09-03 22:38:09] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -87.119023+0.002139j
[2025-09-03 22:38:40] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -86.935003-0.000350j
[2025-09-03 22:39:11] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -86.867526+0.001466j
[2025-09-03 22:39:42] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -86.839968+0.002290j
[2025-09-03 22:40:13] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -86.725167+0.000476j
[2025-09-03 22:40:45] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -86.706819-0.003560j
[2025-09-03 22:41:16] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -86.792133+0.001098j
[2025-09-03 22:41:47] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -86.773297-0.002318j
[2025-09-03 22:42:18] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -86.751179+0.000558j
[2025-09-03 22:42:49] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -86.622019+0.002540j
[2025-09-03 22:43:20] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -86.688313+0.000003j
[2025-09-03 22:43:51] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -86.815101+0.002220j
[2025-09-03 22:44:22] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -86.797426-0.000707j
[2025-09-03 22:44:53] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -86.942602+0.000520j
[2025-09-03 22:45:24] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -86.922672+0.003492j
[2025-09-03 22:45:55] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -86.775901-0.003309j
[2025-09-03 22:46:26] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -86.956902-0.002822j
[2025-09-03 22:46:57] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -86.965464-0.002791j
[2025-09-03 22:46:57] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-09-03 22:47:28] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -86.901356+0.012228j
[2025-09-03 22:47:59] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -86.902421-0.009146j
[2025-09-03 22:48:30] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -86.999614+0.003566j
[2025-09-03 22:49:02] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -87.038654+0.004454j
[2025-09-03 22:49:33] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -86.893602+0.002835j
[2025-09-03 22:50:04] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -86.960528-0.006698j
[2025-09-03 22:50:35] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -86.895544+0.001896j
[2025-09-03 22:51:06] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -86.820234-0.005646j
[2025-09-03 22:51:37] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -86.925677+0.000121j
[2025-09-03 22:52:08] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -87.078299+0.001852j
[2025-09-03 22:52:39] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -86.860164-0.009784j
[2025-09-03 22:53:10] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -86.739616-0.001912j
[2025-09-03 22:53:41] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -86.866829-0.002582j
[2025-09-03 22:54:12] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -86.976690-0.000532j
[2025-09-03 22:54:43] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -86.906521-0.000091j
[2025-09-03 22:55:14] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -86.985629-0.003274j
[2025-09-03 22:55:45] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -86.912692+0.000650j
[2025-09-03 22:56:17] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -86.846625+0.003000j
[2025-09-03 22:56:48] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -86.864422+0.002245j
[2025-09-03 22:57:19] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -86.834871+0.004785j
[2025-09-03 22:57:50] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -86.953241+0.000212j
[2025-09-03 22:58:21] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -86.734381-0.003184j
[2025-09-03 22:58:53] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -86.821595+0.000926j
[2025-09-03 22:59:24] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -86.852370-0.002116j
[2025-09-03 22:59:55] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -86.892214-0.003576j
[2025-09-03 23:00:26] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -87.013480+0.012208j
[2025-09-03 23:00:58] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -86.993633-0.000774j
[2025-09-03 23:01:29] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -86.767892-0.003658j
[2025-09-03 23:02:00] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -86.886049-0.005711j
