[2025-09-03 22:20:02] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.02/training/checkpoints/final_GCNN.pkl
[2025-09-03 22:20:02]   - 迭代次数: final
[2025-09-03 22:20:02]   - 能量: -83.776475-0.001206j ± 0.116201
[2025-09-03 22:20:02]   - 时间戳: 2025-09-03T22:18:24.619356+08:00
[2025-09-03 22:20:15] ✓ 变分状态参数已从checkpoint恢复
[2025-09-03 22:20:15] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-03 22:20:15] ==================================================
[2025-09-03 22:20:15] GCNN for Shastry-Sutherland Model
[2025-09-03 22:20:15] ==================================================
[2025-09-03 22:20:15] System parameters:
[2025-09-03 22:20:15]   - System size: L=5, N=100
[2025-09-03 22:20:15]   - System parameters: J1=0.01, J2=0.0, Q=1.0
[2025-09-03 22:20:15] --------------------------------------------------
[2025-09-03 22:20:15] Model parameters:
[2025-09-03 22:20:15]   - Number of layers = 4
[2025-09-03 22:20:15]   - Number of features = 4
[2025-09-03 22:20:15]   - Total parameters = 19628
[2025-09-03 22:20:15] --------------------------------------------------
[2025-09-03 22:20:15] Training parameters:
[2025-09-03 22:20:15]   - Learning rate: 0.015
[2025-09-03 22:20:15]   - Total iterations: 450
[2025-09-03 22:20:15]   - Annealing cycles: 2
[2025-09-03 22:20:15]   - Initial period: 150
[2025-09-03 22:20:15]   - Period multiplier: 2.0
[2025-09-03 22:20:15]   - Temperature range: 0.0-1.0
[2025-09-03 22:20:15]   - Samples: 4096
[2025-09-03 22:20:15]   - Discarded samples: 0
[2025-09-03 22:20:15]   - Chunk size: 2048
[2025-09-03 22:20:15]   - Diagonal shift: 0.2
[2025-09-03 22:20:15]   - Gradient clipping: 1.0
[2025-09-03 22:20:15]   - Checkpoint enabled: interval=50
[2025-09-03 22:20:15]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.01/training/checkpoints
[2025-09-03 22:20:15] --------------------------------------------------
[2025-09-03 22:20:15] Device status:
[2025-09-03 22:20:15]   - Devices model: NVIDIA H200 NVL
[2025-09-03 22:20:15]   - Number of devices: 1
[2025-09-03 22:20:15]   - Sharding: True
[2025-09-03 22:20:15] ============================================================
[2025-09-03 22:21:17] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -83.134948-0.026302j
[2025-09-03 22:22:05] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -83.170191-0.002059j
[2025-09-03 22:22:36] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -83.108708-0.003970j
[2025-09-03 22:23:07] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -83.096962-0.001039j
[2025-09-03 22:23:38] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -82.989113-0.004919j
[2025-09-03 22:24:08] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -83.087709-0.011041j
[2025-09-03 22:24:39] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -83.056224-0.010489j
[2025-09-03 22:25:10] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -82.978770-0.004128j
[2025-09-03 22:25:41] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -83.060461-0.003522j
[2025-09-03 22:26:11] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -83.041848+0.003284j
[2025-09-03 22:26:42] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -83.060172-0.005749j
[2025-09-03 22:27:13] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -83.032109-0.004436j
[2025-09-03 22:27:44] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -83.032652-0.001495j
[2025-09-03 22:28:14] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -83.074383-0.001720j
[2025-09-03 22:28:45] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -83.055970-0.005193j
[2025-09-03 22:29:16] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -83.082304-0.003496j
[2025-09-03 22:29:47] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -83.151089+0.005877j
[2025-09-03 22:30:18] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -83.153934-0.003119j
[2025-09-03 22:30:48] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -82.875971-0.000066j
[2025-09-03 22:31:19] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -83.014574-0.000328j
[2025-09-03 22:31:50] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -82.985035-0.007666j
[2025-09-03 22:32:21] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -83.023935-0.000500j
[2025-09-03 22:32:52] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -83.082160-0.000996j
[2025-09-03 22:33:22] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -83.078327+0.001348j
[2025-09-03 22:33:53] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -83.054737-0.004264j
[2025-09-03 22:34:24] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -83.224736+0.002757j
[2025-09-03 22:34:55] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -83.188739+0.005371j
[2025-09-03 22:35:26] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -83.171783-0.002389j
[2025-09-03 22:35:56] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -82.988671+0.004898j
[2025-09-03 22:36:27] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -83.109596-0.004309j
[2025-09-03 22:36:58] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -83.266678+0.001176j
[2025-09-03 22:37:29] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -83.291903+0.000541j
[2025-09-03 22:38:00] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -83.152778+0.002325j
[2025-09-03 22:38:30] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -83.221241-0.005541j
[2025-09-03 22:39:01] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -83.128348-0.000625j
[2025-09-03 22:39:32] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -83.238121-0.000758j
[2025-09-03 22:40:03] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -83.072616-0.000486j
[2025-09-03 22:40:34] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -83.188418+0.002833j
[2025-09-03 22:41:04] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -83.128276-0.000518j
[2025-09-03 22:41:35] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -83.035950-0.002229j
[2025-09-03 22:42:06] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -82.972506-0.004612j
[2025-09-03 22:42:37] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -83.122211-0.003687j
[2025-09-03 22:43:08] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -83.124064+0.006573j
[2025-09-03 22:43:38] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -82.891520-0.001697j
[2025-09-03 22:44:09] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -82.981128-0.002675j
[2025-09-03 22:44:40] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -83.099343+0.004186j
[2025-09-03 22:45:10] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -83.118193-0.008859j
[2025-09-03 22:45:41] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -83.072157+0.003458j
[2025-09-03 22:46:12] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -83.274218+0.000669j
[2025-09-03 22:46:43] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -83.183494+0.004142j
[2025-09-03 22:46:43] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-09-03 22:47:13] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -83.320981+0.001510j
[2025-09-03 22:47:44] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -83.262697-0.005135j
[2025-09-03 22:48:15] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -83.215743-0.000533j
[2025-09-03 22:48:46] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -83.208686+0.002914j
[2025-09-03 22:49:16] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -83.150647+0.006293j
[2025-09-03 22:49:47] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -83.114375-0.003321j
[2025-09-03 22:50:18] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -83.044800-0.003206j
[2025-09-03 22:50:49] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -83.026038+0.001135j
[2025-09-03 22:51:19] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -83.189964-0.000426j
[2025-09-03 22:51:50] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -83.257161+0.000205j
[2025-09-03 22:52:21] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -83.273983-0.001520j
[2025-09-03 22:52:52] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -83.262198-0.007478j
[2025-09-03 22:53:22] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -83.266355-0.002577j
[2025-09-03 22:53:53] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -83.190270+0.001185j
[2025-09-03 22:54:24] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -83.259075+0.001734j
[2025-09-03 22:54:54] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -83.175463+0.001241j
[2025-09-03 22:55:25] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -83.145730-0.003813j
[2025-09-03 22:55:56] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -83.058311+0.003792j
[2025-09-03 22:56:27] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -83.125488-0.002044j
[2025-09-03 22:56:57] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -83.253421+0.005980j
[2025-09-03 22:57:28] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -83.018905-0.004676j
[2025-09-03 22:57:59] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -83.019645+0.000291j
[2025-09-03 22:58:30] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -83.113426-0.001320j
[2025-09-03 22:59:01] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -83.142845-0.004631j
[2025-09-03 22:59:31] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -83.220842+0.005266j
[2025-09-03 23:00:02] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -83.209742-0.005055j
[2025-09-03 23:00:33] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -83.141627-0.003391j
[2025-09-03 23:01:04] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -83.176070+0.000057j
[2025-09-03 23:01:34] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -83.131286+0.002279j
[2025-09-03 23:02:05] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -83.176463+0.000091j
