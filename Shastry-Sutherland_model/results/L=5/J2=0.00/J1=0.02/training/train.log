[2025-09-03 18:27:01] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.03/training/checkpoints/final_GCNN.pkl
[2025-09-03 18:27:01]   - 迭代次数: final
[2025-09-03 18:27:01]   - 能量: -84.341578+0.003666j ± 0.112752
[2025-09-03 18:27:01]   - 时间戳: 2025-09-03T18:26:38.802020+08:00
[2025-09-03 18:27:14] ✓ 变分状态参数已从checkpoint恢复
[2025-09-03 18:27:14] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-03 18:27:14] ==================================================
[2025-09-03 18:27:14] GCNN for Shastry-Sutherland Model
[2025-09-03 18:27:14] ==================================================
[2025-09-03 18:27:14] System parameters:
[2025-09-03 18:27:14]   - System size: L=5, N=100
[2025-09-03 18:27:14]   - System parameters: J1=0.02, J2=0.0, Q=1.0
[2025-09-03 18:27:14] --------------------------------------------------
[2025-09-03 18:27:14] Model parameters:
[2025-09-03 18:27:14]   - Number of layers = 4
[2025-09-03 18:27:14]   - Number of features = 4
[2025-09-03 18:27:14]   - Total parameters = 19628
[2025-09-03 18:27:14] --------------------------------------------------
[2025-09-03 18:27:14] Training parameters:
[2025-09-03 18:27:14]   - Learning rate: 0.015
[2025-09-03 18:27:14]   - Total iterations: 450
[2025-09-03 18:27:14]   - Annealing cycles: 2
[2025-09-03 18:27:14]   - Initial period: 150
[2025-09-03 18:27:14]   - Period multiplier: 2.0
[2025-09-03 18:27:14]   - Temperature range: 0.0-1.0
[2025-09-03 18:27:14]   - Samples: 4096
[2025-09-03 18:27:14]   - Discarded samples: 0
[2025-09-03 18:27:14]   - Chunk size: 2048
[2025-09-03 18:27:14]   - Diagonal shift: 0.2
[2025-09-03 18:27:14]   - Gradient clipping: 1.0
[2025-09-03 18:27:14]   - Checkpoint enabled: interval=50
[2025-09-03 18:27:14]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.02/training/checkpoints
[2025-09-03 18:27:14] --------------------------------------------------
[2025-09-03 18:27:14] Device status:
[2025-09-03 18:27:14]   - Devices model: NVIDIA H200 NVL
[2025-09-03 18:27:14]   - Number of devices: 1
[2025-09-03 18:27:14]   - Sharding: True
[2025-09-03 18:27:14] ============================================================
[2025-09-03 18:28:14] [Iter 1/450] R0[0/150], Temp: 1.0000, Energy: -83.952420-0.009293j
[2025-09-03 18:29:01] [Iter 2/450] R0[1/150], Temp: 0.9999, Energy: -83.908978-0.010188j
[2025-09-03 18:29:32] [Iter 3/450] R0[2/150], Temp: 0.9996, Energy: -84.012186-0.008462j
[2025-09-03 18:30:02] [Iter 4/450] R0[3/150], Temp: 0.9990, Energy: -83.878292-0.002189j
[2025-09-03 18:30:33] [Iter 5/450] R0[4/150], Temp: 0.9982, Energy: -83.668500+0.001919j
[2025-09-03 18:31:04] [Iter 6/450] R0[5/150], Temp: 0.9973, Energy: -83.811702+0.003206j
[2025-09-03 18:31:34] [Iter 7/450] R0[6/150], Temp: 0.9961, Energy: -83.741811+0.002758j
[2025-09-03 18:32:05] [Iter 8/450] R0[7/150], Temp: 0.9946, Energy: -83.736674+0.002096j
[2025-09-03 18:32:36] [Iter 9/450] R0[8/150], Temp: 0.9930, Energy: -83.832902+0.003089j
[2025-09-03 18:33:06] [Iter 10/450] R0[9/150], Temp: 0.9911, Energy: -83.989079-0.002455j
[2025-09-03 18:33:37] [Iter 11/450] R0[10/150], Temp: 0.9891, Energy: -83.833296-0.003137j
[2025-09-03 18:34:08] [Iter 12/450] R0[11/150], Temp: 0.9868, Energy: -83.777009+0.000744j
[2025-09-03 18:34:38] [Iter 13/450] R0[12/150], Temp: 0.9843, Energy: -83.893891-0.004883j
[2025-09-03 18:35:09] [Iter 14/450] R0[13/150], Temp: 0.9816, Energy: -83.922380-0.002460j
[2025-09-03 18:35:40] [Iter 15/450] R0[14/150], Temp: 0.9787, Energy: -83.945848-0.001806j
[2025-09-03 18:36:11] [Iter 16/450] R0[15/150], Temp: 0.9755, Energy: -83.885550-0.003260j
[2025-09-03 18:36:41] [Iter 17/450] R0[16/150], Temp: 0.9722, Energy: -83.828100+0.007629j
[2025-09-03 18:37:12] [Iter 18/450] R0[17/150], Temp: 0.9686, Energy: -83.755711-0.003807j
[2025-09-03 18:37:43] [Iter 19/450] R0[18/150], Temp: 0.9649, Energy: -83.554574+0.005596j
[2025-09-03 18:38:13] [Iter 20/450] R0[19/150], Temp: 0.9609, Energy: -83.582660+0.001798j
[2025-09-03 18:38:44] [Iter 21/450] R0[20/150], Temp: 0.9568, Energy: -83.673035-0.001983j
[2025-09-03 18:39:15] [Iter 22/450] R0[21/150], Temp: 0.9524, Energy: -83.668965+0.002147j
[2025-09-03 18:39:45] [Iter 23/450] R0[22/150], Temp: 0.9479, Energy: -83.694921+0.002776j
[2025-09-03 18:40:16] [Iter 24/450] R0[23/150], Temp: 0.9431, Energy: -83.707704-0.002994j
[2025-09-03 18:40:47] [Iter 25/450] R0[24/150], Temp: 0.9382, Energy: -83.928446-0.000799j
[2025-09-03 18:41:17] [Iter 26/450] R0[25/150], Temp: 0.9330, Energy: -83.745890-0.002842j
[2025-09-03 18:41:48] [Iter 27/450] R0[26/150], Temp: 0.9277, Energy: -83.567520+0.001528j
[2025-09-03 18:42:19] [Iter 28/450] R0[27/150], Temp: 0.9222, Energy: -83.601050-0.004412j
[2025-09-03 18:42:49] [Iter 29/450] R0[28/150], Temp: 0.9165, Energy: -83.760367+0.000957j
[2025-09-03 18:43:20] [Iter 30/450] R0[29/150], Temp: 0.9106, Energy: -83.590249+0.002313j
[2025-09-03 18:43:51] [Iter 31/450] R0[30/150], Temp: 0.9045, Energy: -83.653888+0.005431j
[2025-09-03 18:44:21] [Iter 32/450] R0[31/150], Temp: 0.8983, Energy: -83.700895+0.001305j
[2025-09-03 18:44:52] [Iter 33/450] R0[32/150], Temp: 0.8918, Energy: -83.681088+0.000911j
[2025-09-03 18:45:23] [Iter 34/450] R0[33/150], Temp: 0.8853, Energy: -83.734967+0.001777j
[2025-09-03 18:45:54] [Iter 35/450] R0[34/150], Temp: 0.8785, Energy: -83.580768-0.013824j
[2025-09-03 18:46:24] [Iter 36/450] R0[35/150], Temp: 0.8716, Energy: -83.550776+0.005442j
[2025-09-03 18:46:55] [Iter 37/450] R0[36/150], Temp: 0.8645, Energy: -83.898185-0.002915j
[2025-09-03 18:47:26] [Iter 38/450] R0[37/150], Temp: 0.8572, Energy: -83.795989+0.000555j
[2025-09-03 18:47:56] [Iter 39/450] R0[38/150], Temp: 0.8498, Energy: -83.771898+0.003552j
[2025-09-03 18:48:27] [Iter 40/450] R0[39/150], Temp: 0.8423, Energy: -83.674031+0.000737j
[2025-09-03 18:48:58] [Iter 41/450] R0[40/150], Temp: 0.8346, Energy: -83.726884+0.009221j
[2025-09-03 18:49:29] [Iter 42/450] R0[41/150], Temp: 0.8267, Energy: -83.786626+0.003692j
[2025-09-03 18:49:59] [Iter 43/450] R0[42/150], Temp: 0.8187, Energy: -83.779157-0.005194j
[2025-09-03 18:50:30] [Iter 44/450] R0[43/150], Temp: 0.8106, Energy: -83.778839+0.003364j
[2025-09-03 18:51:01] [Iter 45/450] R0[44/150], Temp: 0.8023, Energy: -83.754421-0.015884j
[2025-09-03 18:51:31] [Iter 46/450] R0[45/150], Temp: 0.7939, Energy: -83.799406+0.003558j
[2025-09-03 18:52:02] [Iter 47/450] R0[46/150], Temp: 0.7854, Energy: -83.938531+0.005727j
[2025-09-03 18:52:33] [Iter 48/450] R0[47/150], Temp: 0.7767, Energy: -83.884508-0.003002j
[2025-09-03 18:53:03] [Iter 49/450] R0[48/150], Temp: 0.7679, Energy: -83.739311+0.000011j
[2025-09-03 18:53:34] [Iter 50/450] R0[49/150], Temp: 0.7590, Energy: -83.782904-0.000179j
[2025-09-03 18:53:34] ✓ Checkpoint saved: checkpoint_iter_000050.pkl
[2025-09-03 18:54:05] [Iter 51/450] R0[50/150], Temp: 0.7500, Energy: -83.713756-0.001306j
[2025-09-03 18:54:35] [Iter 52/450] R0[51/150], Temp: 0.7409, Energy: -83.794557-0.002305j
[2025-09-03 18:55:06] [Iter 53/450] R0[52/150], Temp: 0.7316, Energy: -83.729014-0.000769j
[2025-09-03 18:55:37] [Iter 54/450] R0[53/150], Temp: 0.7223, Energy: -83.842609-0.002681j
[2025-09-03 18:56:08] [Iter 55/450] R0[54/150], Temp: 0.7129, Energy: -83.868561+0.000024j
[2025-09-03 18:56:38] [Iter 56/450] R0[55/150], Temp: 0.7034, Energy: -83.965900+0.005354j
[2025-09-03 18:57:09] [Iter 57/450] R0[56/150], Temp: 0.6938, Energy: -84.025994+0.001190j
[2025-09-03 18:57:40] [Iter 58/450] R0[57/150], Temp: 0.6841, Energy: -83.891202+0.004916j
[2025-09-03 18:58:10] [Iter 59/450] R0[58/150], Temp: 0.6743, Energy: -83.875620+0.001568j
[2025-09-03 18:58:41] [Iter 60/450] R0[59/150], Temp: 0.6644, Energy: -83.889387-0.002772j
[2025-09-03 18:59:12] [Iter 61/450] R0[60/150], Temp: 0.6545, Energy: -83.856661-0.004349j
[2025-09-03 18:59:42] [Iter 62/450] R0[61/150], Temp: 0.6445, Energy: -83.844136-0.004760j
[2025-09-03 19:00:13] [Iter 63/450] R0[62/150], Temp: 0.6345, Energy: -83.920488+0.000533j
[2025-09-03 19:00:44] [Iter 64/450] R0[63/150], Temp: 0.6243, Energy: -83.747790-0.001891j
[2025-09-03 19:01:15] [Iter 65/450] R0[64/150], Temp: 0.6142, Energy: -83.725372+0.001132j
[2025-09-03 19:01:45] [Iter 66/450] R0[65/150], Temp: 0.6040, Energy: -83.653586+0.002126j
[2025-09-03 19:02:16] [Iter 67/450] R0[66/150], Temp: 0.5937, Energy: -83.835317+0.001766j
[2025-09-03 19:02:47] [Iter 68/450] R0[67/150], Temp: 0.5834, Energy: -83.828930+0.003991j
[2025-09-03 19:03:17] [Iter 69/450] R0[68/150], Temp: 0.5730, Energy: -83.891175+0.004988j
[2025-09-03 19:03:48] [Iter 70/450] R0[69/150], Temp: 0.5627, Energy: -83.809750-0.003996j
[2025-09-03 19:04:19] [Iter 71/450] R0[70/150], Temp: 0.5523, Energy: -83.941936+0.005459j
[2025-09-03 19:04:49] [Iter 72/450] R0[71/150], Temp: 0.5418, Energy: -83.733858-0.004013j
[2025-09-03 19:05:20] [Iter 73/450] R0[72/150], Temp: 0.5314, Energy: -83.758941+0.006238j
[2025-09-03 19:05:51] [Iter 74/450] R0[73/150], Temp: 0.5209, Energy: -83.771225-0.000722j
[2025-09-03 19:06:21] [Iter 75/450] R0[74/150], Temp: 0.5105, Energy: -83.800415+0.002251j
[2025-09-03 19:06:52] [Iter 76/450] R0[75/150], Temp: 0.5000, Energy: -83.786715+0.002095j
[2025-09-03 19:07:23] [Iter 77/450] R0[76/150], Temp: 0.4895, Energy: -83.805687-0.001419j
[2025-09-03 19:07:53] [Iter 78/450] R0[77/150], Temp: 0.4791, Energy: -83.843504-0.007991j
[2025-09-03 19:08:24] [Iter 79/450] R0[78/150], Temp: 0.4686, Energy: -83.867104-0.002410j
[2025-09-03 19:08:55] [Iter 80/450] R0[79/150], Temp: 0.4582, Energy: -83.995924+0.000111j
[2025-09-03 19:09:26] [Iter 81/450] R0[80/150], Temp: 0.4477, Energy: -83.892449+0.000114j
[2025-09-03 19:09:57] [Iter 82/450] R0[81/150], Temp: 0.4373, Energy: -83.972796+0.001455j
[2025-09-03 19:10:28] [Iter 83/450] R0[82/150], Temp: 0.4270, Energy: -83.987353-0.007059j
[2025-09-03 19:10:59] [Iter 84/450] R0[83/150], Temp: 0.4166, Energy: -83.923601-0.000539j
[2025-09-03 19:11:30] [Iter 85/450] R0[84/150], Temp: 0.4063, Energy: -83.857085-0.005874j
[2025-09-03 19:12:01] [Iter 86/450] R0[85/150], Temp: 0.3960, Energy: -83.866790-0.001768j
[2025-09-03 19:12:32] [Iter 87/450] R0[86/150], Temp: 0.3858, Energy: -83.768073+0.000850j
[2025-09-03 19:13:03] [Iter 88/450] R0[87/150], Temp: 0.3757, Energy: -83.747050+0.001871j
[2025-09-03 19:13:34] [Iter 89/450] R0[88/150], Temp: 0.3655, Energy: -83.797622+0.005836j
[2025-09-03 19:14:04] [Iter 90/450] R0[89/150], Temp: 0.3555, Energy: -83.813769+0.001724j
[2025-09-03 19:14:35] [Iter 91/450] R0[90/150], Temp: 0.3455, Energy: -83.898353+0.004241j
[2025-09-03 19:15:05] [Iter 92/450] R0[91/150], Temp: 0.3356, Energy: -83.991607-0.002028j
[2025-09-03 19:15:36] [Iter 93/450] R0[92/150], Temp: 0.3257, Energy: -83.916815+0.005332j
[2025-09-03 19:16:07] [Iter 94/450] R0[93/150], Temp: 0.3159, Energy: -83.993920+0.009032j
[2025-09-03 19:16:37] [Iter 95/450] R0[94/150], Temp: 0.3062, Energy: -83.931220+0.002285j
[2025-09-03 19:17:08] [Iter 96/450] R0[95/150], Temp: 0.2966, Energy: -83.934984-0.003396j
[2025-09-03 19:17:39] [Iter 97/450] R0[96/150], Temp: 0.2871, Energy: -84.039110+0.001425j
[2025-09-03 19:18:10] [Iter 98/450] R0[97/150], Temp: 0.2777, Energy: -83.916937-0.000287j
[2025-09-03 19:18:40] [Iter 99/450] R0[98/150], Temp: 0.2684, Energy: -84.031564+0.001373j
[2025-09-03 19:19:11] [Iter 100/450] R0[99/150], Temp: 0.2591, Energy: -83.979819+0.008889j
[2025-09-03 19:19:11] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-09-03 19:19:42] [Iter 101/450] R0[100/150], Temp: 0.2500, Energy: -83.939218-0.001609j
[2025-09-03 19:20:12] [Iter 102/450] R0[101/150], Temp: 0.2410, Energy: -83.916032-0.000879j
[2025-09-03 19:20:43] [Iter 103/450] R0[102/150], Temp: 0.2321, Energy: -83.877601+0.003416j
[2025-09-03 19:21:14] [Iter 104/450] R0[103/150], Temp: 0.2233, Energy: -83.800274+0.009216j
[2025-09-03 19:21:44] [Iter 105/450] R0[104/150], Temp: 0.2146, Energy: -83.686330-0.004335j
[2025-09-03 19:22:15] [Iter 106/450] R0[105/150], Temp: 0.2061, Energy: -83.669476-0.003306j
[2025-09-03 19:22:46] [Iter 107/450] R0[106/150], Temp: 0.1977, Energy: -83.748793-0.000758j
[2025-09-03 19:23:16] [Iter 108/450] R0[107/150], Temp: 0.1894, Energy: -83.704735-0.001463j
[2025-09-03 19:23:47] [Iter 109/450] R0[108/150], Temp: 0.1813, Energy: -83.773248-0.002119j
[2025-09-03 19:24:18] [Iter 110/450] R0[109/150], Temp: 0.1733, Energy: -83.814042+0.005793j
[2025-09-03 19:24:48] [Iter 111/450] R0[110/150], Temp: 0.1654, Energy: -83.731611+0.005445j
[2025-09-03 19:25:19] [Iter 112/450] R0[111/150], Temp: 0.1577, Energy: -83.666214-0.001623j
[2025-09-03 19:25:50] [Iter 113/450] R0[112/150], Temp: 0.1502, Energy: -83.679467+0.003183j
[2025-09-03 19:26:21] [Iter 114/450] R0[113/150], Temp: 0.1428, Energy: -83.735483-0.000018j
[2025-09-03 19:26:51] [Iter 115/450] R0[114/150], Temp: 0.1355, Energy: -83.657741-0.001360j
[2025-09-03 19:27:22] [Iter 116/450] R0[115/150], Temp: 0.1284, Energy: -83.818466+0.001774j
[2025-09-03 19:27:53] [Iter 117/450] R0[116/150], Temp: 0.1215, Energy: -83.666534-0.005491j
[2025-09-03 19:28:23] [Iter 118/450] R0[117/150], Temp: 0.1147, Energy: -83.778833-0.000598j
[2025-09-03 19:28:54] [Iter 119/450] R0[118/150], Temp: 0.1082, Energy: -83.815824-0.003429j
[2025-09-03 19:29:25] [Iter 120/450] R0[119/150], Temp: 0.1017, Energy: -83.628281+0.004925j
[2025-09-03 19:29:55] [Iter 121/450] R0[120/150], Temp: 0.0955, Energy: -83.749495+0.002984j
[2025-09-03 19:30:26] [Iter 122/450] R0[121/150], Temp: 0.0894, Energy: -83.777781+0.005446j
[2025-09-03 19:30:57] [Iter 123/450] R0[122/150], Temp: 0.0835, Energy: -83.878558+0.005571j
[2025-09-03 19:31:28] [Iter 124/450] R0[123/150], Temp: 0.0778, Energy: -83.884377-0.001118j
[2025-09-03 19:31:58] [Iter 125/450] R0[124/150], Temp: 0.0723, Energy: -83.843512-0.002319j
[2025-09-03 19:32:29] [Iter 126/450] R0[125/150], Temp: 0.0670, Energy: -83.677263+0.004600j
[2025-09-03 19:33:00] [Iter 127/450] R0[126/150], Temp: 0.0618, Energy: -83.670319+0.000057j
[2025-09-03 19:33:30] [Iter 128/450] R0[127/150], Temp: 0.0569, Energy: -83.602423+0.000093j
[2025-09-03 19:34:01] [Iter 129/450] R0[128/150], Temp: 0.0521, Energy: -83.586222-0.004183j
[2025-09-03 19:34:32] [Iter 130/450] R0[129/150], Temp: 0.0476, Energy: -83.554470+0.001081j
[2025-09-03 19:35:02] [Iter 131/450] R0[130/150], Temp: 0.0432, Energy: -83.715566+0.002938j
[2025-09-03 19:35:33] [Iter 132/450] R0[131/150], Temp: 0.0391, Energy: -83.851960-0.005144j
[2025-09-03 19:36:04] [Iter 133/450] R0[132/150], Temp: 0.0351, Energy: -83.631817+0.002969j
[2025-09-03 19:36:35] [Iter 134/450] R0[133/150], Temp: 0.0314, Energy: -83.670489+0.004896j
[2025-09-03 19:37:05] [Iter 135/450] R0[134/150], Temp: 0.0278, Energy: -83.484249-0.000270j
[2025-09-03 19:37:36] [Iter 136/450] R0[135/150], Temp: 0.0245, Energy: -83.590005-0.003013j
[2025-09-03 19:38:07] [Iter 137/450] R0[136/150], Temp: 0.0213, Energy: -83.569324+0.001583j
[2025-09-03 19:38:37] [Iter 138/450] R0[137/150], Temp: 0.0184, Energy: -83.685204+0.002080j
[2025-09-03 19:39:08] [Iter 139/450] R0[138/150], Temp: 0.0157, Energy: -83.844249-0.003868j
[2025-09-03 19:39:39] [Iter 140/450] R0[139/150], Temp: 0.0132, Energy: -83.666392-0.001433j
[2025-09-03 19:40:10] [Iter 141/450] R0[140/150], Temp: 0.0109, Energy: -83.709153-0.004881j
[2025-09-03 19:40:40] [Iter 142/450] R0[141/150], Temp: 0.0089, Energy: -83.602795+0.001180j
[2025-09-03 19:41:11] [Iter 143/450] R0[142/150], Temp: 0.0070, Energy: -83.653703-0.004007j
[2025-09-03 19:41:42] [Iter 144/450] R0[143/150], Temp: 0.0054, Energy: -83.616557+0.003777j
[2025-09-03 19:42:12] [Iter 145/450] R0[144/150], Temp: 0.0039, Energy: -83.646516+0.001453j
[2025-09-03 19:42:43] [Iter 146/450] R0[145/150], Temp: 0.0027, Energy: -83.674155-0.002253j
[2025-09-03 19:43:14] [Iter 147/450] R0[146/150], Temp: 0.0018, Energy: -83.642708-0.002064j
[2025-09-03 19:43:44] [Iter 148/450] R0[147/150], Temp: 0.0010, Energy: -83.714166+0.002123j
[2025-09-03 19:44:15] [Iter 149/450] R0[148/150], Temp: 0.0004, Energy: -83.729539-0.009400j
[2025-09-03 19:44:46] [Iter 150/450] R0[149/150], Temp: 0.0001, Energy: -83.757526-0.004757j
[2025-09-03 19:44:46] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-09-03 19:44:46] RESTART #1 | Period: 300
[2025-09-03 19:45:16] [Iter 151/450] R1[0/300], Temp: 1.0000, Energy: -83.672569-0.006197j
[2025-09-03 19:45:47] [Iter 152/450] R1[1/300], Temp: 1.0000, Energy: -83.762532-0.001296j
[2025-09-03 19:46:18] [Iter 153/450] R1[2/300], Temp: 0.9999, Energy: -83.749911+0.001943j
[2025-09-03 19:46:48] [Iter 154/450] R1[3/300], Temp: 0.9998, Energy: -83.855758+0.001525j
[2025-09-03 19:47:19] [Iter 155/450] R1[4/300], Temp: 0.9996, Energy: -83.856955-0.000644j
[2025-09-03 19:47:50] [Iter 156/450] R1[5/300], Temp: 0.9993, Energy: -83.800360-0.000369j
[2025-09-03 19:48:21] [Iter 157/450] R1[6/300], Temp: 0.9990, Energy: -83.776437-0.005270j
[2025-09-03 19:48:51] [Iter 158/450] R1[7/300], Temp: 0.9987, Energy: -83.869331-0.001163j
[2025-09-03 19:49:22] [Iter 159/450] R1[8/300], Temp: 0.9982, Energy: -83.896433-0.011546j
[2025-09-03 19:49:53] [Iter 160/450] R1[9/300], Temp: 0.9978, Energy: -83.955960-0.006998j
[2025-09-03 19:50:23] [Iter 161/450] R1[10/300], Temp: 0.9973, Energy: -83.944529-0.002961j
[2025-09-03 19:50:54] [Iter 162/450] R1[11/300], Temp: 0.9967, Energy: -83.841464-0.002854j
[2025-09-03 19:51:25] [Iter 163/450] R1[12/300], Temp: 0.9961, Energy: -83.893781-0.007273j
[2025-09-03 19:51:56] [Iter 164/450] R1[13/300], Temp: 0.9954, Energy: -83.848484-0.000250j
[2025-09-03 19:52:26] [Iter 165/450] R1[14/300], Temp: 0.9946, Energy: -83.763677-0.007213j
[2025-09-03 19:52:57] [Iter 166/450] R1[15/300], Temp: 0.9938, Energy: -83.704738-0.002257j
[2025-09-03 19:53:28] [Iter 167/450] R1[16/300], Temp: 0.9930, Energy: -83.730880-0.004325j
[2025-09-03 19:53:58] [Iter 168/450] R1[17/300], Temp: 0.9921, Energy: -83.859911+0.001251j
[2025-09-03 19:54:29] [Iter 169/450] R1[18/300], Temp: 0.9911, Energy: -83.826963-0.002391j
[2025-09-03 19:55:00] [Iter 170/450] R1[19/300], Temp: 0.9901, Energy: -83.783722-0.006597j
[2025-09-03 19:55:30] [Iter 171/450] R1[20/300], Temp: 0.9891, Energy: -83.827006+0.003322j
[2025-09-03 19:56:01] [Iter 172/450] R1[21/300], Temp: 0.9880, Energy: -83.657090+0.003258j
[2025-09-03 19:56:32] [Iter 173/450] R1[22/300], Temp: 0.9868, Energy: -83.638446-0.008236j
[2025-09-03 19:57:02] [Iter 174/450] R1[23/300], Temp: 0.9856, Energy: -83.698347+0.002866j
[2025-09-03 19:57:33] [Iter 175/450] R1[24/300], Temp: 0.9843, Energy: -83.811593-0.008032j
[2025-09-03 19:58:04] [Iter 176/450] R1[25/300], Temp: 0.9830, Energy: -83.663169-0.000959j
[2025-09-03 19:58:34] [Iter 177/450] R1[26/300], Temp: 0.9816, Energy: -83.674792+0.003571j
[2025-09-03 19:59:05] [Iter 178/450] R1[27/300], Temp: 0.9801, Energy: -83.769685+0.000249j
[2025-09-03 19:59:36] [Iter 179/450] R1[28/300], Temp: 0.9787, Energy: -83.931653+0.000094j
[2025-09-03 20:00:06] [Iter 180/450] R1[29/300], Temp: 0.9771, Energy: -83.703526+0.000001j
[2025-09-03 20:00:37] [Iter 181/450] R1[30/300], Temp: 0.9755, Energy: -83.701202+0.000067j
[2025-09-03 20:01:08] [Iter 182/450] R1[31/300], Temp: 0.9739, Energy: -83.800353-0.001493j
[2025-09-03 20:01:39] [Iter 183/450] R1[32/300], Temp: 0.9722, Energy: -83.721003-0.003249j
[2025-09-03 20:02:09] [Iter 184/450] R1[33/300], Temp: 0.9704, Energy: -83.667402-0.000964j
[2025-09-03 20:02:40] [Iter 185/450] R1[34/300], Temp: 0.9686, Energy: -83.840050+0.001543j
[2025-09-03 20:03:11] [Iter 186/450] R1[35/300], Temp: 0.9668, Energy: -83.956509+0.003823j
[2025-09-03 20:03:41] [Iter 187/450] R1[36/300], Temp: 0.9649, Energy: -83.866276-0.001810j
[2025-09-03 20:04:12] [Iter 188/450] R1[37/300], Temp: 0.9629, Energy: -83.844956-0.000062j
[2025-09-03 20:04:43] [Iter 189/450] R1[38/300], Temp: 0.9609, Energy: -83.736657-0.001125j
[2025-09-03 20:05:13] [Iter 190/450] R1[39/300], Temp: 0.9589, Energy: -83.730429+0.002493j
[2025-09-03 20:05:44] [Iter 191/450] R1[40/300], Temp: 0.9568, Energy: -83.701452-0.011822j
[2025-09-03 20:06:15] [Iter 192/450] R1[41/300], Temp: 0.9546, Energy: -83.668809-0.001203j
[2025-09-03 20:06:45] [Iter 193/450] R1[42/300], Temp: 0.9524, Energy: -83.721948-0.001279j
[2025-09-03 20:07:16] [Iter 194/450] R1[43/300], Temp: 0.9502, Energy: -83.904375+0.002977j
[2025-09-03 20:07:47] [Iter 195/450] R1[44/300], Temp: 0.9479, Energy: -83.887652+0.000145j
[2025-09-03 20:08:17] [Iter 196/450] R1[45/300], Temp: 0.9455, Energy: -83.899712+0.002844j
[2025-09-03 20:08:48] [Iter 197/450] R1[46/300], Temp: 0.9431, Energy: -83.834345-0.000923j
[2025-09-03 20:09:19] [Iter 198/450] R1[47/300], Temp: 0.9407, Energy: -83.794824+0.006108j
[2025-09-03 20:09:49] [Iter 199/450] R1[48/300], Temp: 0.9382, Energy: -83.744450+0.001643j
[2025-09-03 20:10:20] [Iter 200/450] R1[49/300], Temp: 0.9356, Energy: -83.874579-0.000628j
[2025-09-03 20:10:20] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-09-03 20:10:51] [Iter 201/450] R1[50/300], Temp: 0.9330, Energy: -83.949842+0.000537j
[2025-09-03 20:11:22] [Iter 202/450] R1[51/300], Temp: 0.9304, Energy: -83.887941-0.010006j
[2025-09-03 20:11:52] [Iter 203/450] R1[52/300], Temp: 0.9277, Energy: -83.869776-0.000297j
[2025-09-03 20:12:23] [Iter 204/450] R1[53/300], Temp: 0.9249, Energy: -83.782795-0.002315j
[2025-09-03 20:12:54] [Iter 205/450] R1[54/300], Temp: 0.9222, Energy: -83.819204+0.003782j
[2025-09-03 20:13:24] [Iter 206/450] R1[55/300], Temp: 0.9193, Energy: -83.739945+0.009547j
[2025-09-03 20:13:55] [Iter 207/450] R1[56/300], Temp: 0.9165, Energy: -83.849810-0.002138j
[2025-09-03 20:14:26] [Iter 208/450] R1[57/300], Temp: 0.9135, Energy: -83.793901+0.005663j
[2025-09-03 20:14:56] [Iter 209/450] R1[58/300], Temp: 0.9106, Energy: -83.677156+0.000121j
[2025-09-03 20:15:27] [Iter 210/450] R1[59/300], Temp: 0.9076, Energy: -83.748953-0.002789j
[2025-09-03 20:15:58] [Iter 211/450] R1[60/300], Temp: 0.9045, Energy: -83.737097-0.000035j
[2025-09-03 20:16:29] [Iter 212/450] R1[61/300], Temp: 0.9014, Energy: -83.776070-0.005966j
[2025-09-03 20:16:59] [Iter 213/450] R1[62/300], Temp: 0.8983, Energy: -83.843387-0.005471j
[2025-09-03 20:17:30] [Iter 214/450] R1[63/300], Temp: 0.8951, Energy: -83.759625-0.001359j
[2025-09-03 20:18:01] [Iter 215/450] R1[64/300], Temp: 0.8918, Energy: -83.859758+0.004931j
[2025-09-03 20:18:31] [Iter 216/450] R1[65/300], Temp: 0.8886, Energy: -83.825598+0.001589j
[2025-09-03 20:19:02] [Iter 217/450] R1[66/300], Temp: 0.8853, Energy: -83.941770-0.001806j
[2025-09-03 20:19:33] [Iter 218/450] R1[67/300], Temp: 0.8819, Energy: -83.875413-0.005052j
[2025-09-03 20:20:03] [Iter 219/450] R1[68/300], Temp: 0.8785, Energy: -83.998656+0.002326j
[2025-09-03 20:20:34] [Iter 220/450] R1[69/300], Temp: 0.8751, Energy: -83.856267+0.002249j
[2025-09-03 20:21:05] [Iter 221/450] R1[70/300], Temp: 0.8716, Energy: -83.803967+0.001026j
[2025-09-03 20:21:36] [Iter 222/450] R1[71/300], Temp: 0.8680, Energy: -83.844757-0.001377j
[2025-09-03 20:22:06] [Iter 223/450] R1[72/300], Temp: 0.8645, Energy: -83.829572-0.002973j
[2025-09-03 20:22:37] [Iter 224/450] R1[73/300], Temp: 0.8609, Energy: -83.816049-0.001416j
[2025-09-03 20:23:08] [Iter 225/450] R1[74/300], Temp: 0.8572, Energy: -83.788801-0.004788j
[2025-09-03 20:23:38] [Iter 226/450] R1[75/300], Temp: 0.8536, Energy: -83.759114+0.001954j
[2025-09-03 20:24:09] [Iter 227/450] R1[76/300], Temp: 0.8498, Energy: -83.616403+0.006990j
[2025-09-03 20:24:40] [Iter 228/450] R1[77/300], Temp: 0.8461, Energy: -83.708074+0.006595j
[2025-09-03 20:25:10] [Iter 229/450] R1[78/300], Temp: 0.8423, Energy: -83.751304+0.002528j
[2025-09-03 20:25:41] [Iter 230/450] R1[79/300], Temp: 0.8384, Energy: -83.865506+0.000898j
[2025-09-03 20:26:12] [Iter 231/450] R1[80/300], Temp: 0.8346, Energy: -83.627519-0.001711j
[2025-09-03 20:26:42] [Iter 232/450] R1[81/300], Temp: 0.8307, Energy: -83.732905-0.004390j
[2025-09-03 20:27:13] [Iter 233/450] R1[82/300], Temp: 0.8267, Energy: -83.786160-0.006254j
[2025-09-03 20:27:44] [Iter 234/450] R1[83/300], Temp: 0.8227, Energy: -83.947975+0.000567j
[2025-09-03 20:28:14] [Iter 235/450] R1[84/300], Temp: 0.8187, Energy: -83.658119+0.002426j
[2025-09-03 20:28:45] [Iter 236/450] R1[85/300], Temp: 0.8147, Energy: -83.666873+0.005047j
[2025-09-03 20:29:16] [Iter 237/450] R1[86/300], Temp: 0.8106, Energy: -83.873264+0.000727j
[2025-09-03 20:29:46] [Iter 238/450] R1[87/300], Temp: 0.8065, Energy: -83.894430+0.008150j
[2025-09-03 20:30:17] [Iter 239/450] R1[88/300], Temp: 0.8023, Energy: -83.758110+0.000383j
[2025-09-03 20:30:48] [Iter 240/450] R1[89/300], Temp: 0.7981, Energy: -83.729736-0.002014j
[2025-09-03 20:31:18] [Iter 241/450] R1[90/300], Temp: 0.7939, Energy: -83.807779-0.000861j
[2025-09-03 20:31:49] [Iter 242/450] R1[91/300], Temp: 0.7896, Energy: -83.830143+0.002893j
[2025-09-03 20:32:20] [Iter 243/450] R1[92/300], Temp: 0.7854, Energy: -83.870850-0.003262j
[2025-09-03 20:32:50] [Iter 244/450] R1[93/300], Temp: 0.7810, Energy: -83.834722-0.003469j
[2025-09-03 20:33:21] [Iter 245/450] R1[94/300], Temp: 0.7767, Energy: -83.776581+0.007806j
[2025-09-03 20:33:52] [Iter 246/450] R1[95/300], Temp: 0.7723, Energy: -83.783423+0.005070j
[2025-09-03 20:34:22] [Iter 247/450] R1[96/300], Temp: 0.7679, Energy: -83.833168+0.003014j
[2025-09-03 20:34:53] [Iter 248/450] R1[97/300], Temp: 0.7635, Energy: -83.614584+0.005385j
[2025-09-03 20:35:24] [Iter 249/450] R1[98/300], Temp: 0.7590, Energy: -83.632982+0.000074j
[2025-09-03 20:35:54] [Iter 250/450] R1[99/300], Temp: 0.7545, Energy: -83.723172+0.001037j
[2025-09-03 20:35:54] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-03 20:36:25] [Iter 251/450] R1[100/300], Temp: 0.7500, Energy: -83.497813-0.000469j
[2025-09-03 20:36:56] [Iter 252/450] R1[101/300], Temp: 0.7455, Energy: -83.655125+0.003780j
[2025-09-03 20:37:26] [Iter 253/450] R1[102/300], Temp: 0.7409, Energy: -83.623833-0.001400j
[2025-09-03 20:37:57] [Iter 254/450] R1[103/300], Temp: 0.7363, Energy: -83.615325-0.003438j
[2025-09-03 20:38:28] [Iter 255/450] R1[104/300], Temp: 0.7316, Energy: -83.710210+0.003519j
[2025-09-03 20:38:59] [Iter 256/450] R1[105/300], Temp: 0.7270, Energy: -83.751048+0.001345j
[2025-09-03 20:39:29] [Iter 257/450] R1[106/300], Temp: 0.7223, Energy: -83.973689-0.004167j
[2025-09-03 20:40:00] [Iter 258/450] R1[107/300], Temp: 0.7176, Energy: -83.903102-0.000152j
[2025-09-03 20:40:31] [Iter 259/450] R1[108/300], Temp: 0.7129, Energy: -83.705984+0.000733j
[2025-09-03 20:41:01] [Iter 260/450] R1[109/300], Temp: 0.7081, Energy: -83.805888+0.007392j
[2025-09-03 20:41:32] [Iter 261/450] R1[110/300], Temp: 0.7034, Energy: -83.811716+0.002441j
[2025-09-03 20:42:03] [Iter 262/450] R1[111/300], Temp: 0.6986, Energy: -83.870628-0.004199j
[2025-09-03 20:42:33] [Iter 263/450] R1[112/300], Temp: 0.6938, Energy: -83.839065-0.002596j
[2025-09-03 20:43:04] [Iter 264/450] R1[113/300], Temp: 0.6889, Energy: -83.988810-0.002015j
[2025-09-03 20:43:35] [Iter 265/450] R1[114/300], Temp: 0.6841, Energy: -83.968488-0.006133j
[2025-09-03 20:44:05] [Iter 266/450] R1[115/300], Temp: 0.6792, Energy: -83.917805-0.001848j
[2025-09-03 20:44:36] [Iter 267/450] R1[116/300], Temp: 0.6743, Energy: -83.908088+0.004830j
[2025-09-03 20:45:07] [Iter 268/450] R1[117/300], Temp: 0.6694, Energy: -83.814333+0.002370j
[2025-09-03 20:45:37] [Iter 269/450] R1[118/300], Temp: 0.6644, Energy: -83.734617+0.001671j
[2025-09-03 20:46:08] [Iter 270/450] R1[119/300], Temp: 0.6595, Energy: -83.729277+0.003624j
[2025-09-03 20:46:39] [Iter 271/450] R1[120/300], Temp: 0.6545, Energy: -83.831172+0.004943j
[2025-09-03 20:47:10] [Iter 272/450] R1[121/300], Temp: 0.6495, Energy: -83.676466+0.001296j
[2025-09-03 20:47:40] [Iter 273/450] R1[122/300], Temp: 0.6445, Energy: -83.736148-0.006854j
[2025-09-03 20:48:11] [Iter 274/450] R1[123/300], Temp: 0.6395, Energy: -83.548491+0.000826j
[2025-09-03 20:48:42] [Iter 275/450] R1[124/300], Temp: 0.6345, Energy: -83.818247-0.001581j
[2025-09-03 20:49:12] [Iter 276/450] R1[125/300], Temp: 0.6294, Energy: -83.715193+0.000943j
[2025-09-03 20:49:43] [Iter 277/450] R1[126/300], Temp: 0.6243, Energy: -83.717486-0.005393j
[2025-09-03 20:50:14] [Iter 278/450] R1[127/300], Temp: 0.6193, Energy: -83.748843+0.003484j
[2025-09-03 20:50:44] [Iter 279/450] R1[128/300], Temp: 0.6142, Energy: -83.803738-0.002584j
[2025-09-03 20:51:15] [Iter 280/450] R1[129/300], Temp: 0.6091, Energy: -83.847524+0.003160j
[2025-09-03 20:51:46] [Iter 281/450] R1[130/300], Temp: 0.6040, Energy: -83.691434-0.000957j
[2025-09-03 20:52:16] [Iter 282/450] R1[131/300], Temp: 0.5988, Energy: -83.655934-0.000784j
[2025-09-03 20:52:47] [Iter 283/450] R1[132/300], Temp: 0.5937, Energy: -83.608772-0.001699j
[2025-09-03 20:53:18] [Iter 284/450] R1[133/300], Temp: 0.5885, Energy: -83.628519-0.002135j
[2025-09-03 20:53:48] [Iter 285/450] R1[134/300], Temp: 0.5834, Energy: -83.606246+0.000551j
[2025-09-03 20:54:19] [Iter 286/450] R1[135/300], Temp: 0.5782, Energy: -83.590842+0.000095j
[2025-09-03 20:54:50] [Iter 287/450] R1[136/300], Temp: 0.5730, Energy: -83.760853+0.002551j
[2025-09-03 20:55:21] [Iter 288/450] R1[137/300], Temp: 0.5679, Energy: -83.646747+0.003689j
[2025-09-03 20:55:51] [Iter 289/450] R1[138/300], Temp: 0.5627, Energy: -83.709176-0.007354j
[2025-09-03 20:56:22] [Iter 290/450] R1[139/300], Temp: 0.5575, Energy: -83.762960+0.004882j
[2025-09-03 20:56:53] [Iter 291/450] R1[140/300], Temp: 0.5523, Energy: -83.901966+0.000872j
[2025-09-03 20:57:23] [Iter 292/450] R1[141/300], Temp: 0.5471, Energy: -83.804058-0.005395j
[2025-09-03 20:57:54] [Iter 293/450] R1[142/300], Temp: 0.5418, Energy: -83.611339+0.000630j
[2025-09-03 20:58:25] [Iter 294/450] R1[143/300], Temp: 0.5366, Energy: -83.773090-0.005143j
[2025-09-03 20:58:55] [Iter 295/450] R1[144/300], Temp: 0.5314, Energy: -83.781912-0.003312j
[2025-09-03 20:59:26] [Iter 296/450] R1[145/300], Temp: 0.5262, Energy: -83.695207+0.001654j
[2025-09-03 20:59:57] [Iter 297/450] R1[146/300], Temp: 0.5209, Energy: -83.727918-0.001372j
[2025-09-03 21:00:27] [Iter 298/450] R1[147/300], Temp: 0.5157, Energy: -83.671098+0.002844j
[2025-09-03 21:00:58] [Iter 299/450] R1[148/300], Temp: 0.5105, Energy: -83.684657-0.004573j
[2025-09-03 21:01:29] [Iter 300/450] R1[149/300], Temp: 0.5052, Energy: -83.785486+0.000203j
[2025-09-03 21:01:29] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-09-03 21:01:59] [Iter 301/450] R1[150/300], Temp: 0.5000, Energy: -83.902388-0.000548j
[2025-09-03 21:02:30] [Iter 302/450] R1[151/300], Temp: 0.4948, Energy: -83.857960-0.000479j
[2025-09-03 21:03:01] [Iter 303/450] R1[152/300], Temp: 0.4895, Energy: -83.829125+0.002318j
[2025-09-03 21:03:31] [Iter 304/450] R1[153/300], Temp: 0.4843, Energy: -84.045794+0.002390j
[2025-09-03 21:04:02] [Iter 305/450] R1[154/300], Temp: 0.4791, Energy: -83.790308-0.000911j
[2025-09-03 21:04:33] [Iter 306/450] R1[155/300], Temp: 0.4738, Energy: -83.806138-0.001543j
[2025-09-03 21:05:04] [Iter 307/450] R1[156/300], Temp: 0.4686, Energy: -83.702000-0.001773j
[2025-09-03 21:05:34] [Iter 308/450] R1[157/300], Temp: 0.4634, Energy: -83.818564+0.005409j
[2025-09-03 21:06:05] [Iter 309/450] R1[158/300], Temp: 0.4582, Energy: -83.722171+0.002608j
[2025-09-03 21:06:36] [Iter 310/450] R1[159/300], Temp: 0.4529, Energy: -84.034644-0.000417j
[2025-09-03 21:07:06] [Iter 311/450] R1[160/300], Temp: 0.4477, Energy: -83.697521-0.001210j
[2025-09-03 21:07:37] [Iter 312/450] R1[161/300], Temp: 0.4425, Energy: -83.897816+0.002115j
[2025-09-03 21:08:08] [Iter 313/450] R1[162/300], Temp: 0.4373, Energy: -83.923562-0.000246j
[2025-09-03 21:08:38] [Iter 314/450] R1[163/300], Temp: 0.4321, Energy: -83.952226+0.004517j
[2025-09-03 21:09:09] [Iter 315/450] R1[164/300], Temp: 0.4270, Energy: -83.902507-0.005040j
[2025-09-03 21:09:40] [Iter 316/450] R1[165/300], Temp: 0.4218, Energy: -83.910783+0.000113j
[2025-09-03 21:10:10] [Iter 317/450] R1[166/300], Temp: 0.4166, Energy: -83.742674+0.005408j
[2025-09-03 21:10:41] [Iter 318/450] R1[167/300], Temp: 0.4115, Energy: -83.722182-0.004819j
[2025-09-03 21:11:12] [Iter 319/450] R1[168/300], Temp: 0.4063, Energy: -83.730554+0.001036j
[2025-09-03 21:11:42] [Iter 320/450] R1[169/300], Temp: 0.4012, Energy: -83.814256+0.005709j
[2025-09-03 21:12:13] [Iter 321/450] R1[170/300], Temp: 0.3960, Energy: -83.819612-0.002099j
[2025-09-03 21:12:44] [Iter 322/450] R1[171/300], Temp: 0.3909, Energy: -83.845785-0.005214j
[2025-09-03 21:13:14] [Iter 323/450] R1[172/300], Temp: 0.3858, Energy: -83.968130-0.004990j
[2025-09-03 21:13:45] [Iter 324/450] R1[173/300], Temp: 0.3807, Energy: -83.929618+0.000430j
[2025-09-03 21:14:16] [Iter 325/450] R1[174/300], Temp: 0.3757, Energy: -83.716097-0.003423j
[2025-09-03 21:14:47] [Iter 326/450] R1[175/300], Temp: 0.3706, Energy: -83.582099+0.001146j
[2025-09-03 21:15:17] [Iter 327/450] R1[176/300], Temp: 0.3655, Energy: -83.729802+0.001021j
[2025-09-03 21:15:48] [Iter 328/450] R1[177/300], Temp: 0.3605, Energy: -83.766839+0.009265j
[2025-09-03 21:16:19] [Iter 329/450] R1[178/300], Temp: 0.3555, Energy: -83.861738+0.001414j
[2025-09-03 21:16:49] [Iter 330/450] R1[179/300], Temp: 0.3505, Energy: -83.956458-0.003941j
[2025-09-03 21:17:20] [Iter 331/450] R1[180/300], Temp: 0.3455, Energy: -83.848278+0.002460j
[2025-09-03 21:17:51] [Iter 332/450] R1[181/300], Temp: 0.3405, Energy: -83.868574-0.000528j
[2025-09-03 21:18:21] [Iter 333/450] R1[182/300], Temp: 0.3356, Energy: -83.900409-0.004437j
[2025-09-03 21:18:52] [Iter 334/450] R1[183/300], Temp: 0.3306, Energy: -83.771205-0.002213j
[2025-09-03 21:19:23] [Iter 335/450] R1[184/300], Temp: 0.3257, Energy: -83.671039+0.000809j
[2025-09-03 21:19:53] [Iter 336/450] R1[185/300], Temp: 0.3208, Energy: -83.726072+0.001699j
[2025-09-03 21:20:24] [Iter 337/450] R1[186/300], Temp: 0.3159, Energy: -83.793806+0.000319j
[2025-09-03 21:20:55] [Iter 338/450] R1[187/300], Temp: 0.3111, Energy: -83.763769+0.001232j
[2025-09-03 21:21:25] [Iter 339/450] R1[188/300], Temp: 0.3062, Energy: -83.822691-0.002445j
[2025-09-03 21:21:56] [Iter 340/450] R1[189/300], Temp: 0.3014, Energy: -83.752994+0.000330j
[2025-09-03 21:22:27] [Iter 341/450] R1[190/300], Temp: 0.2966, Energy: -83.991008+0.003855j
[2025-09-03 21:22:57] [Iter 342/450] R1[191/300], Temp: 0.2919, Energy: -83.998533-0.002743j
[2025-09-03 21:23:28] [Iter 343/450] R1[192/300], Temp: 0.2871, Energy: -83.613052-0.000170j
[2025-09-03 21:23:59] [Iter 344/450] R1[193/300], Temp: 0.2824, Energy: -83.798837+0.000000j
[2025-09-03 21:24:29] [Iter 345/450] R1[194/300], Temp: 0.2777, Energy: -83.845566+0.001984j
[2025-09-03 21:25:00] [Iter 346/450] R1[195/300], Temp: 0.2730, Energy: -83.825023-0.001346j
[2025-09-03 21:25:31] [Iter 347/450] R1[196/300], Temp: 0.2684, Energy: -83.748786+0.003419j
[2025-09-03 21:26:01] [Iter 348/450] R1[197/300], Temp: 0.2637, Energy: -83.647603+0.007550j
[2025-09-03 21:26:32] [Iter 349/450] R1[198/300], Temp: 0.2591, Energy: -83.679765-0.000990j
[2025-09-03 21:27:03] [Iter 350/450] R1[199/300], Temp: 0.2545, Energy: -83.596437+0.003458j
[2025-09-03 21:27:03] ✓ Checkpoint saved: checkpoint_iter_000350.pkl
[2025-09-03 21:27:34] [Iter 351/450] R1[200/300], Temp: 0.2500, Energy: -83.767980+0.002620j
[2025-09-03 21:28:04] [Iter 352/450] R1[201/300], Temp: 0.2455, Energy: -83.856860+0.000937j
[2025-09-03 21:28:35] [Iter 353/450] R1[202/300], Temp: 0.2410, Energy: -83.747815+0.004488j
[2025-09-03 21:29:06] [Iter 354/450] R1[203/300], Temp: 0.2365, Energy: -83.622157-0.004264j
[2025-09-03 21:29:36] [Iter 355/450] R1[204/300], Temp: 0.2321, Energy: -83.700181-0.001491j
[2025-09-03 21:30:07] [Iter 356/450] R1[205/300], Temp: 0.2277, Energy: -83.669236-0.003143j
[2025-09-03 21:30:38] [Iter 357/450] R1[206/300], Temp: 0.2233, Energy: -83.743935+0.001796j
[2025-09-03 21:31:08] [Iter 358/450] R1[207/300], Temp: 0.2190, Energy: -83.766073+0.004590j
[2025-09-03 21:31:39] [Iter 359/450] R1[208/300], Temp: 0.2146, Energy: -83.860676+0.000414j
[2025-09-03 21:32:10] [Iter 360/450] R1[209/300], Temp: 0.2104, Energy: -83.878000-0.001751j
[2025-09-03 21:32:40] [Iter 361/450] R1[210/300], Temp: 0.2061, Energy: -83.765286-0.004144j
[2025-09-03 21:33:11] [Iter 362/450] R1[211/300], Temp: 0.2019, Energy: -83.917881-0.003537j
[2025-09-03 21:33:42] [Iter 363/450] R1[212/300], Temp: 0.1977, Energy: -83.836971-0.000943j
[2025-09-03 21:34:12] [Iter 364/450] R1[213/300], Temp: 0.1935, Energy: -83.926522-0.000816j
[2025-09-03 21:34:43] [Iter 365/450] R1[214/300], Temp: 0.1894, Energy: -83.800091+0.000637j
[2025-09-03 21:35:14] [Iter 366/450] R1[215/300], Temp: 0.1853, Energy: -83.728079-0.002200j
[2025-09-03 21:35:44] [Iter 367/450] R1[216/300], Temp: 0.1813, Energy: -83.771560-0.006790j
[2025-09-03 21:36:15] [Iter 368/450] R1[217/300], Temp: 0.1773, Energy: -83.795284-0.003265j
[2025-09-03 21:36:46] [Iter 369/450] R1[218/300], Temp: 0.1733, Energy: -83.635867+0.008701j
[2025-09-03 21:37:16] [Iter 370/450] R1[219/300], Temp: 0.1693, Energy: -83.783886-0.003247j
[2025-09-03 21:37:47] [Iter 371/450] R1[220/300], Temp: 0.1654, Energy: -83.963486+0.001405j
[2025-09-03 21:38:18] [Iter 372/450] R1[221/300], Temp: 0.1616, Energy: -83.788370+0.007033j
[2025-09-03 21:38:48] [Iter 373/450] R1[222/300], Temp: 0.1577, Energy: -83.817564-0.003545j
[2025-09-03 21:39:19] [Iter 374/450] R1[223/300], Temp: 0.1539, Energy: -83.962333-0.000461j
[2025-09-03 21:39:50] [Iter 375/450] R1[224/300], Temp: 0.1502, Energy: -83.887135+0.000217j
[2025-09-03 21:40:20] [Iter 376/450] R1[225/300], Temp: 0.1464, Energy: -83.837894+0.005500j
[2025-09-03 21:40:51] [Iter 377/450] R1[226/300], Temp: 0.1428, Energy: -83.782535+0.000771j
[2025-09-03 21:41:22] [Iter 378/450] R1[227/300], Temp: 0.1391, Energy: -83.890979+0.011685j
[2025-09-03 21:41:52] [Iter 379/450] R1[228/300], Temp: 0.1355, Energy: -83.997588+0.000801j
[2025-09-03 21:42:23] [Iter 380/450] R1[229/300], Temp: 0.1320, Energy: -83.808354+0.000524j
[2025-09-03 21:42:54] [Iter 381/450] R1[230/300], Temp: 0.1284, Energy: -83.767046+0.000106j
[2025-09-03 21:43:25] [Iter 382/450] R1[231/300], Temp: 0.1249, Energy: -83.797494-0.001519j
[2025-09-03 21:43:55] [Iter 383/450] R1[232/300], Temp: 0.1215, Energy: -83.871461+0.004665j
[2025-09-03 21:44:26] [Iter 384/450] R1[233/300], Temp: 0.1181, Energy: -83.777791+0.000316j
[2025-09-03 21:44:57] [Iter 385/450] R1[234/300], Temp: 0.1147, Energy: -83.628407-0.004214j
[2025-09-03 21:45:27] [Iter 386/450] R1[235/300], Temp: 0.1114, Energy: -83.689449-0.005285j
[2025-09-03 21:45:58] [Iter 387/450] R1[236/300], Temp: 0.1082, Energy: -83.694806-0.002256j
[2025-09-03 21:46:29] [Iter 388/450] R1[237/300], Temp: 0.1049, Energy: -83.726155-0.000010j
[2025-09-03 21:46:59] [Iter 389/450] R1[238/300], Temp: 0.1017, Energy: -83.672026-0.002825j
[2025-09-03 21:47:30] [Iter 390/450] R1[239/300], Temp: 0.0986, Energy: -83.656057+0.001987j
[2025-09-03 21:48:01] [Iter 391/450] R1[240/300], Temp: 0.0955, Energy: -83.747023+0.003954j
[2025-09-03 21:48:31] [Iter 392/450] R1[241/300], Temp: 0.0924, Energy: -83.901911-0.003551j
[2025-09-03 21:49:02] [Iter 393/450] R1[242/300], Temp: 0.0894, Energy: -83.840410+0.002734j
[2025-09-03 21:49:33] [Iter 394/450] R1[243/300], Temp: 0.0865, Energy: -83.761284+0.000676j
[2025-09-03 21:50:03] [Iter 395/450] R1[244/300], Temp: 0.0835, Energy: -83.804321+0.000663j
[2025-09-03 21:50:34] [Iter 396/450] R1[245/300], Temp: 0.0807, Energy: -83.919048+0.003677j
[2025-09-03 21:51:05] [Iter 397/450] R1[246/300], Temp: 0.0778, Energy: -83.883592+0.000046j
[2025-09-03 21:51:35] [Iter 398/450] R1[247/300], Temp: 0.0751, Energy: -83.855907-0.008424j
[2025-09-03 21:52:06] [Iter 399/450] R1[248/300], Temp: 0.0723, Energy: -83.868310-0.001499j
[2025-09-03 21:52:37] [Iter 400/450] R1[249/300], Temp: 0.0696, Energy: -83.894894-0.003416j
[2025-09-03 21:52:37] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-09-03 21:53:07] [Iter 401/450] R1[250/300], Temp: 0.0670, Energy: -83.846020+0.004290j
[2025-09-03 21:53:38] [Iter 402/450] R1[251/300], Temp: 0.0644, Energy: -83.804145+0.000812j
[2025-09-03 21:54:09] [Iter 403/450] R1[252/300], Temp: 0.0618, Energy: -83.733375+0.003052j
[2025-09-03 21:54:39] [Iter 404/450] R1[253/300], Temp: 0.0593, Energy: -83.749930+0.001843j
[2025-09-03 21:55:10] [Iter 405/450] R1[254/300], Temp: 0.0569, Energy: -83.699050+0.002628j
[2025-09-03 21:55:41] [Iter 406/450] R1[255/300], Temp: 0.0545, Energy: -83.735239+0.000517j
[2025-09-03 21:56:12] [Iter 407/450] R1[256/300], Temp: 0.0521, Energy: -83.556250-0.005815j
[2025-09-03 21:56:42] [Iter 408/450] R1[257/300], Temp: 0.0498, Energy: -83.726969-0.004666j
[2025-09-03 21:57:13] [Iter 409/450] R1[258/300], Temp: 0.0476, Energy: -83.705666-0.001471j
[2025-09-03 21:57:44] [Iter 410/450] R1[259/300], Temp: 0.0454, Energy: -83.804845-0.003774j
[2025-09-03 21:58:14] [Iter 411/450] R1[260/300], Temp: 0.0432, Energy: -83.939957+0.002225j
[2025-09-03 21:58:45] [Iter 412/450] R1[261/300], Temp: 0.0411, Energy: -83.926999-0.004303j
[2025-09-03 21:59:16] [Iter 413/450] R1[262/300], Temp: 0.0391, Energy: -83.783588+0.003364j
[2025-09-03 21:59:47] [Iter 414/450] R1[263/300], Temp: 0.0371, Energy: -83.834855-0.005482j
[2025-09-03 22:00:17] [Iter 415/450] R1[264/300], Temp: 0.0351, Energy: -83.783054-0.001142j
[2025-09-03 22:00:48] [Iter 416/450] R1[265/300], Temp: 0.0332, Energy: -83.800774+0.004571j
[2025-09-03 22:01:19] [Iter 417/450] R1[266/300], Temp: 0.0314, Energy: -83.739449+0.005339j
[2025-09-03 22:01:49] [Iter 418/450] R1[267/300], Temp: 0.0296, Energy: -83.692292-0.001112j
[2025-09-03 22:02:20] [Iter 419/450] R1[268/300], Temp: 0.0278, Energy: -83.782895-0.005190j
[2025-09-03 22:02:51] [Iter 420/450] R1[269/300], Temp: 0.0261, Energy: -83.734079-0.003142j
[2025-09-03 22:03:21] [Iter 421/450] R1[270/300], Temp: 0.0245, Energy: -83.723140+0.001987j
[2025-09-03 22:03:52] [Iter 422/450] R1[271/300], Temp: 0.0229, Energy: -83.691236-0.004920j
[2025-09-03 22:04:23] [Iter 423/450] R1[272/300], Temp: 0.0213, Energy: -83.888067+0.002970j
[2025-09-03 22:04:53] [Iter 424/450] R1[273/300], Temp: 0.0199, Energy: -83.745496+0.004616j
[2025-09-03 22:05:24] [Iter 425/450] R1[274/300], Temp: 0.0184, Energy: -83.823581-0.002175j
[2025-09-03 22:05:55] [Iter 426/450] R1[275/300], Temp: 0.0170, Energy: -83.720423-0.001206j
[2025-09-03 22:06:26] [Iter 427/450] R1[276/300], Temp: 0.0157, Energy: -83.752139+0.001780j
[2025-09-03 22:06:56] [Iter 428/450] R1[277/300], Temp: 0.0144, Energy: -83.833251-0.010618j
[2025-09-03 22:07:27] [Iter 429/450] R1[278/300], Temp: 0.0132, Energy: -83.833897-0.001818j
[2025-09-03 22:07:58] [Iter 430/450] R1[279/300], Temp: 0.0120, Energy: -83.691157-0.001726j
[2025-09-03 22:08:28] [Iter 431/450] R1[280/300], Temp: 0.0109, Energy: -83.735363-0.005188j
[2025-09-03 22:08:59] [Iter 432/450] R1[281/300], Temp: 0.0099, Energy: -83.750678-0.004220j
[2025-09-03 22:09:30] [Iter 433/450] R1[282/300], Temp: 0.0089, Energy: -83.931670-0.004516j
[2025-09-03 22:10:00] [Iter 434/450] R1[283/300], Temp: 0.0079, Energy: -83.919675+0.001065j
[2025-09-03 22:10:31] [Iter 435/450] R1[284/300], Temp: 0.0070, Energy: -83.926798-0.004333j
[2025-09-03 22:11:02] [Iter 436/450] R1[285/300], Temp: 0.0062, Energy: -83.929671+0.001000j
[2025-09-03 22:11:32] [Iter 437/450] R1[286/300], Temp: 0.0054, Energy: -83.953191-0.000344j
[2025-09-03 22:12:03] [Iter 438/450] R1[287/300], Temp: 0.0046, Energy: -84.050460-0.002671j
[2025-09-03 22:12:34] [Iter 439/450] R1[288/300], Temp: 0.0039, Energy: -83.924106+0.005760j
[2025-09-03 22:13:04] [Iter 440/450] R1[289/300], Temp: 0.0033, Energy: -84.058140+0.002455j
[2025-09-03 22:13:35] [Iter 441/450] R1[290/300], Temp: 0.0027, Energy: -83.922546+0.000068j
[2025-09-03 22:14:06] [Iter 442/450] R1[291/300], Temp: 0.0022, Energy: -83.821096+0.004408j
[2025-09-03 22:14:36] [Iter 443/450] R1[292/300], Temp: 0.0018, Energy: -83.788074+0.000062j
[2025-09-03 22:15:07] [Iter 444/450] R1[293/300], Temp: 0.0013, Energy: -83.830291-0.006163j
[2025-09-03 22:15:38] [Iter 445/450] R1[294/300], Temp: 0.0010, Energy: -83.708605-0.001904j
[2025-09-03 22:16:08] [Iter 446/450] R1[295/300], Temp: 0.0007, Energy: -83.654709-0.003503j
[2025-09-03 22:16:39] [Iter 447/450] R1[296/300], Temp: 0.0004, Energy: -83.751518-0.010047j
[2025-09-03 22:17:10] [Iter 448/450] R1[297/300], Temp: 0.0002, Energy: -83.897443-0.005539j
[2025-09-03 22:17:41] [Iter 449/450] R1[298/300], Temp: 0.0001, Energy: -83.742714+0.001839j
[2025-09-03 22:18:12] [Iter 450/450] R1[299/300], Temp: 0.0000, Energy: -83.776475-0.001206j
[2025-09-03 22:18:12] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-09-03 22:18:12] ✅ Training completed | Restarts: 1
[2025-09-03 22:18:12] ============================================================
[2025-09-03 22:18:12] Training completed | Runtime: 13857.6s
[2025-09-03 22:18:24] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-03 22:18:24] ============================================================
