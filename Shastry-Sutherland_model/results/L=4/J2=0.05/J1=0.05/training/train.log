[2025-09-03 14:33:26] ==================================================
[2025-09-03 14:33:26] GCNN for Shastry-Sutherland Model
[2025-09-03 14:33:26] ==================================================
[2025-09-03 14:33:26] System parameters:
[2025-09-03 14:33:26]   - System size: L=4, N=64
[2025-09-03 14:33:26]   - System parameters: J1=0.05, J2=0.05, Q=0.95
[2025-09-03 14:33:26] --------------------------------------------------
[2025-09-03 14:33:26] Model parameters:
[2025-09-03 14:33:26]   - Number of layers = 4
[2025-09-03 14:33:26]   - Number of features = 4
[2025-09-03 14:33:26]   - Total parameters = 12572
[2025-09-03 14:33:26] --------------------------------------------------
[2025-09-03 14:33:26] Training parameters:
[2025-09-03 14:33:26]   - Learning rate: 0.015
[2025-09-03 14:33:26]   - Total iterations: 2250
[2025-09-03 14:33:26]   - Annealing cycles: 4
[2025-09-03 14:33:26]   - Initial period: 150
[2025-09-03 14:33:26]   - Period multiplier: 2.0
[2025-09-03 14:33:26]   - Temperature range: 0.0-1.0
[2025-09-03 14:33:26]   - Samples: 16384
[2025-09-03 14:33:26]   - Discarded samples: 0
[2025-09-03 14:33:26]   - Chunk size: 2048
[2025-09-03 14:33:26]   - Diagonal shift: 0.2
[2025-09-03 14:33:26]   - Gradient clipping: 1.0
[2025-09-03 14:33:26]   - Checkpoint enabled: interval=250
[2025-09-03 14:33:26]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.05/training/checkpoints
[2025-09-03 14:33:26] --------------------------------------------------
[2025-09-03 14:33:26] Device status:
[2025-09-03 14:33:26]   - Devices model: NVIDIA H200 NVL
[2025-09-03 14:33:26]   - Number of devices: 1
[2025-09-03 14:33:26]   - Sharding: True
[2025-09-03 14:33:26] ============================================================
[2025-09-03 14:34:30] [Iter 1/2250] R0[0/150], Temp: 1.0000, Energy: 1.999447-0.000204j
[2025-09-03 14:35:05] [Iter 2/2250] R0[1/150], Temp: 0.9999, Energy: 1.999181-0.000099j
[2025-09-03 14:36:05] [Iter 3/2250] R0[2/150], Temp: 0.9996, Energy: 1.999051+0.000136j
[2025-09-03 14:37:05] [Iter 4/2250] R0[3/150], Temp: 0.9990, Energy: 1.999250-0.000439j
[2025-09-03 14:38:05] [Iter 5/2250] R0[4/150], Temp: 0.9982, Energy: 1.999254-0.000183j
[2025-09-03 14:39:05] [Iter 6/2250] R0[5/150], Temp: 0.9973, Energy: 1.999246-0.000076j
[2025-09-03 14:40:05] [Iter 7/2250] R0[6/150], Temp: 0.9961, Energy: 1.998957+0.000103j
[2025-09-03 14:41:05] [Iter 8/2250] R0[7/150], Temp: 0.9946, Energy: 1.998956-0.000060j
[2025-09-03 14:42:05] [Iter 9/2250] R0[8/150], Temp: 0.9930, Energy: 1.998902-0.000206j
[2025-09-03 14:43:05] [Iter 10/2250] R0[9/150], Temp: 0.9911, Energy: 1.999033+0.000059j
[2025-09-03 14:44:05] [Iter 11/2250] R0[10/150], Temp: 0.9891, Energy: 1.999290-0.000128j
[2025-09-03 14:45:05] [Iter 12/2250] R0[11/150], Temp: 0.9868, Energy: 1.999120+0.000031j
[2025-09-03 14:46:06] [Iter 13/2250] R0[12/150], Temp: 0.9843, Energy: 1.998825-0.000083j
[2025-09-03 14:47:06] [Iter 14/2250] R0[13/150], Temp: 0.9816, Energy: 1.999158-0.000154j
[2025-09-03 14:48:06] [Iter 15/2250] R0[14/150], Temp: 0.9787, Energy: 1.999190+0.000149j
[2025-09-03 14:49:06] [Iter 16/2250] R0[15/150], Temp: 0.9755, Energy: 1.999101-0.000268j
[2025-09-03 14:50:06] [Iter 17/2250] R0[16/150], Temp: 0.9722, Energy: 1.999202+0.000319j
[2025-09-03 14:51:07] [Iter 18/2250] R0[17/150], Temp: 0.9686, Energy: 1.998889-0.000115j
[2025-09-03 14:52:07] [Iter 19/2250] R0[18/150], Temp: 0.9649, Energy: 1.998793+0.000291j
[2025-09-03 14:53:07] [Iter 20/2250] R0[19/150], Temp: 0.9609, Energy: 1.999006+0.000193j
[2025-09-03 14:54:07] [Iter 21/2250] R0[20/150], Temp: 0.9568, Energy: 1.999125-0.000048j
[2025-09-03 14:55:07] [Iter 22/2250] R0[21/150], Temp: 0.9524, Energy: 1.998981-0.000111j
[2025-09-03 14:56:08] [Iter 23/2250] R0[22/150], Temp: 0.9479, Energy: 1.999116+0.000183j
[2025-09-03 14:57:08] [Iter 24/2250] R0[23/150], Temp: 0.9431, Energy: 1.999019-0.000157j
[2025-09-03 14:58:08] [Iter 25/2250] R0[24/150], Temp: 0.9382, Energy: 1.998683+0.000124j
[2025-09-03 14:59:08] [Iter 26/2250] R0[25/150], Temp: 0.9330, Energy: 1.999059-0.000087j
[2025-09-03 15:00:08] [Iter 27/2250] R0[26/150], Temp: 0.9277, Energy: 1.998546+0.000095j
[2025-09-03 15:01:08] [Iter 28/2250] R0[27/150], Temp: 0.9222, Energy: 1.998572+0.000058j
[2025-09-03 15:02:08] [Iter 29/2250] R0[28/150], Temp: 0.9165, Energy: 1.998698+0.000057j
[2025-09-03 15:03:08] [Iter 30/2250] R0[29/150], Temp: 0.9106, Energy: 1.998665+0.000086j
[2025-09-03 15:04:09] [Iter 31/2250] R0[30/150], Temp: 0.9045, Energy: 1.999267-0.000061j
[2025-09-03 15:05:09] [Iter 32/2250] R0[31/150], Temp: 0.8983, Energy: 1.998054+0.000296j
[2025-09-03 15:06:09] [Iter 33/2250] R0[32/150], Temp: 0.8918, Energy: 1.998328+0.000239j
[2025-09-03 15:07:09] [Iter 34/2250] R0[33/150], Temp: 0.8853, Energy: 1.998597+0.000250j
[2025-09-03 15:08:09] [Iter 35/2250] R0[34/150], Temp: 0.8785, Energy: 1.998222+0.000189j
[2025-09-03 15:09:09] [Iter 36/2250] R0[35/150], Temp: 0.8716, Energy: 1.998565+0.000089j
[2025-09-03 15:10:09] [Iter 37/2250] R0[36/150], Temp: 0.8645, Energy: 1.998539-0.000280j
[2025-09-03 15:11:09] [Iter 38/2250] R0[37/150], Temp: 0.8572, Energy: 1.998555-0.000102j
[2025-09-03 15:12:09] [Iter 39/2250] R0[38/150], Temp: 0.8498, Energy: 1.998856-0.000649j
[2025-09-03 15:13:09] [Iter 40/2250] R0[39/150], Temp: 0.8423, Energy: 1.998125-0.000348j
[2025-09-03 15:14:09] [Iter 41/2250] R0[40/150], Temp: 0.8346, Energy: 1.998584-0.000021j
[2025-09-03 15:15:10] [Iter 42/2250] R0[41/150], Temp: 0.8267, Energy: 1.997920+0.000149j
[2025-09-03 15:16:10] [Iter 43/2250] R0[42/150], Temp: 0.8187, Energy: 1.998348-0.000193j
[2025-09-03 15:17:10] [Iter 44/2250] R0[43/150], Temp: 0.8106, Energy: 1.998173+0.000043j
[2025-09-03 15:18:10] [Iter 45/2250] R0[44/150], Temp: 0.8023, Energy: 1.997812+0.000109j
[2025-09-03 15:19:10] [Iter 46/2250] R0[45/150], Temp: 0.7939, Energy: 1.997833+0.000105j
[2025-09-03 15:20:10] [Iter 47/2250] R0[46/150], Temp: 0.7854, Energy: 1.997982-0.000166j
[2025-09-03 15:21:10] [Iter 48/2250] R0[47/150], Temp: 0.7767, Energy: 1.998744-0.000500j
[2025-09-03 15:22:10] [Iter 49/2250] R0[48/150], Temp: 0.7679, Energy: 1.997472+0.000230j
[2025-09-03 15:23:10] [Iter 50/2250] R0[49/150], Temp: 0.7590, Energy: 1.998039-0.000275j
[2025-09-03 15:24:10] [Iter 51/2250] R0[50/150], Temp: 0.7500, Energy: 1.997479-0.000231j
[2025-09-03 15:25:10] [Iter 52/2250] R0[51/150], Temp: 0.7409, Energy: 1.997314+0.000012j
[2025-09-03 15:26:10] [Iter 53/2250] R0[52/150], Temp: 0.7316, Energy: 1.997709+0.000369j
[2025-09-03 15:27:11] [Iter 54/2250] R0[53/150], Temp: 0.7223, Energy: 1.996855+0.000058j
[2025-09-03 15:28:11] [Iter 55/2250] R0[54/150], Temp: 0.7129, Energy: 1.997917-0.000033j
[2025-09-03 15:29:11] [Iter 56/2250] R0[55/150], Temp: 0.7034, Energy: 1.996978+0.000007j
[2025-09-03 15:30:11] [Iter 57/2250] R0[56/150], Temp: 0.6938, Energy: 1.996265+0.000340j
[2025-09-03 15:31:11] [Iter 58/2250] R0[57/150], Temp: 0.6841, Energy: 1.996646+0.000218j
[2025-09-03 15:32:11] [Iter 59/2250] R0[58/150], Temp: 0.6743, Energy: 1.997634+0.000009j
[2025-09-03 15:33:11] [Iter 60/2250] R0[59/150], Temp: 0.6644, Energy: 1.997141-0.000021j
[2025-09-03 15:34:11] [Iter 61/2250] R0[60/150], Temp: 0.6545, Energy: 1.995691+0.000117j
[2025-09-03 15:35:11] [Iter 62/2250] R0[61/150], Temp: 0.6445, Energy: 1.996155+0.000362j
[2025-09-03 15:36:11] [Iter 63/2250] R0[62/150], Temp: 0.6345, Energy: 1.995529+0.000023j
[2025-09-03 15:37:11] [Iter 64/2250] R0[63/150], Temp: 0.6243, Energy: 1.994660+0.000070j
[2025-09-03 15:38:12] [Iter 65/2250] R0[64/150], Temp: 0.6142, Energy: 1.995829+0.000094j
[2025-09-03 15:39:12] [Iter 66/2250] R0[65/150], Temp: 0.6040, Energy: 1.995646+0.000319j
[2025-09-03 15:40:12] [Iter 67/2250] R0[66/150], Temp: 0.5937, Energy: 1.995264+0.000287j
[2025-09-03 15:41:12] [Iter 68/2250] R0[67/150], Temp: 0.5834, Energy: 1.996318-0.000322j
[2025-09-03 15:42:12] [Iter 69/2250] R0[68/150], Temp: 0.5730, Energy: 1.994356-0.000050j
[2025-09-03 15:43:12] [Iter 70/2250] R0[69/150], Temp: 0.5627, Energy: 1.993077+0.000765j
[2025-09-03 15:44:12] [Iter 71/2250] R0[70/150], Temp: 0.5523, Energy: 1.993988-0.000112j
[2025-09-03 15:45:12] [Iter 72/2250] R0[71/150], Temp: 0.5418, Energy: 1.993987+0.000031j
[2025-09-03 15:46:12] [Iter 73/2250] R0[72/150], Temp: 0.5314, Energy: 1.994080-0.000097j
[2025-09-03 15:47:12] [Iter 74/2250] R0[73/150], Temp: 0.5209, Energy: 1.992186+0.000499j
[2025-09-03 15:48:13] [Iter 75/2250] R0[74/150], Temp: 0.5105, Energy: 1.993921-0.000388j
[2025-09-03 15:49:13] [Iter 76/2250] R0[75/150], Temp: 0.5000, Energy: 1.989988+0.000549j
[2025-09-03 15:50:13] [Iter 77/2250] R0[76/150], Temp: 0.4895, Energy: 1.992254-0.000247j
[2025-09-03 15:51:13] [Iter 78/2250] R0[77/150], Temp: 0.4791, Energy: 1.989475-0.000380j
[2025-09-03 15:52:13] [Iter 79/2250] R0[78/150], Temp: 0.4686, Energy: 1.990795-0.000483j
[2025-09-03 15:53:13] [Iter 80/2250] R0[79/150], Temp: 0.4582, Energy: 1.988506+0.000127j
[2025-09-03 15:54:13] [Iter 81/2250] R0[80/150], Temp: 0.4477, Energy: 1.988450-0.000085j
[2025-09-03 15:55:13] [Iter 82/2250] R0[81/150], Temp: 0.4373, Energy: 1.988386+0.000076j
[2025-09-03 15:56:13] [Iter 83/2250] R0[82/150], Temp: 0.4270, Energy: 1.989814-0.000254j
[2025-09-03 15:57:13] [Iter 84/2250] R0[83/150], Temp: 0.4166, Energy: 1.988105-0.000291j
[2025-09-03 15:58:13] [Iter 85/2250] R0[84/150], Temp: 0.4063, Energy: 1.987650+0.000071j
[2025-09-03 15:59:13] [Iter 86/2250] R0[85/150], Temp: 0.3960, Energy: 1.983420+0.000690j
[2025-09-03 16:00:14] [Iter 87/2250] R0[86/150], Temp: 0.3858, Energy: 1.981669+0.000597j
[2025-09-03 16:01:14] [Iter 88/2250] R0[87/150], Temp: 0.3757, Energy: 1.983615-0.000729j
[2025-09-03 16:02:14] [Iter 89/2250] R0[88/150], Temp: 0.3655, Energy: 1.979654-0.000487j
[2025-09-03 16:03:14] [Iter 90/2250] R0[89/150], Temp: 0.3555, Energy: 1.977696+0.000789j
[2025-09-03 16:04:14] [Iter 91/2250] R0[90/150], Temp: 0.3455, Energy: 1.976635+0.000322j
[2025-09-03 16:05:14] [Iter 92/2250] R0[91/150], Temp: 0.3356, Energy: 1.976466-0.000401j
[2025-09-03 16:06:14] [Iter 93/2250] R0[92/150], Temp: 0.3257, Energy: 1.972764-0.000090j
[2025-09-03 16:07:14] [Iter 94/2250] R0[93/150], Temp: 0.3159, Energy: 1.970772+0.001041j
[2025-09-03 16:08:14] [Iter 95/2250] R0[94/150], Temp: 0.3062, Energy: 1.968084-0.000291j
[2025-09-03 16:09:14] [Iter 96/2250] R0[95/150], Temp: 0.2966, Energy: 1.970683-0.001129j
[2025-09-03 16:10:14] [Iter 97/2250] R0[96/150], Temp: 0.2871, Energy: 1.962219+0.000548j
[2025-09-03 16:11:14] [Iter 98/2250] R0[97/150], Temp: 0.2777, Energy: 1.960658+0.000179j
[2025-09-03 16:12:15] [Iter 99/2250] R0[98/150], Temp: 0.2684, Energy: 1.952778+0.001188j
[2025-09-03 16:13:15] [Iter 100/2250] R0[99/150], Temp: 0.2591, Energy: 1.955472-0.000160j
[2025-09-03 16:14:15] [Iter 101/2250] R0[100/150], Temp: 0.2500, Energy: 1.950269-0.001734j
[2025-09-03 16:15:15] [Iter 102/2250] R0[101/150], Temp: 0.2410, Energy: 1.942685+0.000985j
[2025-09-03 16:16:15] [Iter 103/2250] R0[102/150], Temp: 0.2321, Energy: 1.940611-0.000655j
[2025-09-03 16:17:15] [Iter 104/2250] R0[103/150], Temp: 0.2233, Energy: 1.936234-0.001655j
[2025-09-03 16:18:16] [Iter 105/2250] R0[104/150], Temp: 0.2146, Energy: 1.924698+0.001259j
[2025-09-03 16:19:16] [Iter 106/2250] R0[105/150], Temp: 0.2061, Energy: 1.922043-0.002148j
[2025-09-03 16:20:16] [Iter 107/2250] R0[106/150], Temp: 0.1977, Energy: 1.910807+0.000461j
[2025-09-03 16:21:16] [Iter 108/2250] R0[107/150], Temp: 0.1894, Energy: 1.898791+0.001626j
[2025-09-03 16:22:16] [Iter 109/2250] R0[108/150], Temp: 0.1813, Energy: 1.888510-0.000262j
[2025-09-03 16:23:16] [Iter 110/2250] R0[109/150], Temp: 0.1733, Energy: 1.887161-0.001923j
[2025-09-03 16:24:17] [Iter 111/2250] R0[110/150], Temp: 0.1654, Energy: 1.867673+0.001998j
[2025-09-03 16:25:17] [Iter 112/2250] R0[111/150], Temp: 0.1577, Energy: 1.855703+0.000971j
[2025-09-03 16:26:17] [Iter 113/2250] R0[112/150], Temp: 0.1502, Energy: 1.834906+0.000513j
[2025-09-03 16:27:17] [Iter 114/2250] R0[113/150], Temp: 0.1428, Energy: 1.828890-0.002233j
[2025-09-03 16:28:17] [Iter 115/2250] R0[114/150], Temp: 0.1355, Energy: 1.810994-0.003403j
[2025-09-03 16:29:17] [Iter 116/2250] R0[115/150], Temp: 0.1284, Energy: 1.775617-0.001434j
[2025-09-03 16:30:17] [Iter 117/2250] R0[116/150], Temp: 0.1215, Energy: 1.753855+0.000452j
[2025-09-03 16:31:18] [Iter 118/2250] R0[117/150], Temp: 0.1147, Energy: 1.735776-0.001953j
[2025-09-03 16:32:18] [Iter 119/2250] R0[118/150], Temp: 0.1082, Energy: 1.701742-0.000655j
[2025-09-03 16:33:18] [Iter 120/2250] R0[119/150], Temp: 0.1017, Energy: 1.671554+0.000549j
[2025-09-03 16:34:18] [Iter 121/2250] R0[120/150], Temp: 0.0955, Energy: 1.617729-0.000796j
[2025-09-03 16:35:18] [Iter 122/2250] R0[121/150], Temp: 0.0894, Energy: 1.585877+0.000544j
[2025-09-03 16:36:18] [Iter 123/2250] R0[122/150], Temp: 0.0835, Energy: 1.527934+0.001578j
[2025-09-03 16:37:19] [Iter 124/2250] R0[123/150], Temp: 0.0778, Energy: 1.480114+0.001518j
[2025-09-03 16:38:19] [Iter 125/2250] R0[124/150], Temp: 0.0723, Energy: 1.439287-0.002717j
[2025-09-03 16:39:19] [Iter 126/2250] R0[125/150], Temp: 0.0670, Energy: 1.365797+0.002255j
[2025-09-03 16:40:19] [Iter 127/2250] R0[126/150], Temp: 0.0618, Energy: 1.303281+0.003299j
[2025-09-03 16:41:19] [Iter 128/2250] R0[127/150], Temp: 0.0569, Energy: 1.214056+0.002464j
[2025-09-03 16:42:19] [Iter 129/2250] R0[128/150], Temp: 0.0521, Energy: 1.126474+0.007168j
[2025-09-03 16:43:20] [Iter 130/2250] R0[129/150], Temp: 0.0476, Energy: 1.043131+0.003022j
[2025-09-03 16:44:20] [Iter 131/2250] R0[130/150], Temp: 0.0432, Energy: 0.953267-0.000204j
[2025-09-03 16:45:20] [Iter 132/2250] R0[131/150], Temp: 0.0391, Energy: 0.840741-0.003712j
[2025-09-03 16:46:20] [Iter 133/2250] R0[132/150], Temp: 0.0351, Energy: 0.722695-0.004316j
[2025-09-03 16:47:20] [Iter 134/2250] R0[133/150], Temp: 0.0314, Energy: 0.632606-0.001082j
[2025-09-03 16:48:20] [Iter 135/2250] R0[134/150], Temp: 0.0278, Energy: 0.455630+0.006890j
[2025-09-03 16:49:21] [Iter 136/2250] R0[135/150], Temp: 0.0245, Energy: 0.323559+0.004672j
[2025-09-03 16:50:21] [Iter 137/2250] R0[136/150], Temp: 0.0213, Energy: 0.113395+0.009369j
[2025-09-03 16:51:21] [Iter 138/2250] R0[137/150], Temp: 0.0184, Energy: -0.071358+0.016807j
[2025-09-03 16:52:21] [Iter 139/2250] R0[138/150], Temp: 0.0157, Energy: -0.266037+0.001296j
[2025-09-03 16:53:21] [Iter 140/2250] R0[139/150], Temp: 0.0132, Energy: -0.450392-0.004357j
[2025-09-03 16:54:21] [Iter 141/2250] R0[140/150], Temp: 0.0109, Energy: -0.717079+0.005767j
[2025-09-03 16:55:22] [Iter 142/2250] R0[141/150], Temp: 0.0089, Energy: -0.910944-0.003692j
[2025-09-03 16:56:22] [Iter 143/2250] R0[142/150], Temp: 0.0070, Energy: -1.255749+0.001808j
[2025-09-03 16:57:22] [Iter 144/2250] R0[143/150], Temp: 0.0054, Energy: -1.512828-0.025288j
[2025-09-03 16:58:22] [Iter 145/2250] R0[144/150], Temp: 0.0039, Energy: -1.868854+0.023592j
[2025-09-03 16:59:22] [Iter 146/2250] R0[145/150], Temp: 0.0027, Energy: -2.181189-0.018741j
[2025-09-03 17:00:22] [Iter 147/2250] R0[146/150], Temp: 0.0018, Energy: -2.571917-0.010622j
[2025-09-03 17:01:23] [Iter 148/2250] R0[147/150], Temp: 0.0010, Energy: -2.976091-0.023860j
[2025-09-03 17:02:23] [Iter 149/2250] R0[148/150], Temp: 0.0004, Energy: -3.424678+0.017190j
[2025-09-03 17:03:23] [Iter 150/2250] R0[149/150], Temp: 0.0001, Energy: -3.913803+0.025455j
[2025-09-03 17:03:23] RESTART #1 | Period: 300
[2025-09-03 17:04:23] [Iter 151/2250] R1[0/300], Temp: 1.0000, Energy: -4.360001-0.025805j
[2025-09-03 17:05:23] [Iter 152/2250] R1[1/300], Temp: 1.0000, Energy: -4.857268+0.004999j
[2025-09-03 17:06:23] [Iter 153/2250] R1[2/300], Temp: 0.9999, Energy: -5.421362-0.021828j
[2025-09-03 17:07:24] [Iter 154/2250] R1[3/300], Temp: 0.9998, Energy: -6.056029-0.018849j
[2025-09-03 17:08:24] [Iter 155/2250] R1[4/300], Temp: 0.9996, Energy: -6.545637-0.025181j
[2025-09-03 17:09:24] [Iter 156/2250] R1[5/300], Temp: 0.9993, Energy: -7.232065-0.032592j
[2025-09-03 17:10:24] [Iter 157/2250] R1[6/300], Temp: 0.9990, Energy: -7.932383+0.035096j
[2025-09-03 17:11:24] [Iter 158/2250] R1[7/300], Temp: 0.9987, Energy: -8.598659-0.034532j
[2025-09-03 17:12:25] [Iter 159/2250] R1[8/300], Temp: 0.9982, Energy: -9.411337-0.003300j
[2025-09-03 17:13:25] [Iter 160/2250] R1[9/300], Temp: 0.9978, Energy: -10.101243-0.027973j
[2025-09-03 17:14:25] [Iter 161/2250] R1[10/300], Temp: 0.9973, Energy: -10.984430-0.004029j
[2025-09-03 17:15:25] [Iter 162/2250] R1[11/300], Temp: 0.9967, Energy: -11.839379-0.051403j
[2025-09-03 17:16:25] [Iter 163/2250] R1[12/300], Temp: 0.9961, Energy: -12.654898-0.073978j
[2025-09-03 17:17:25] [Iter 164/2250] R1[13/300], Temp: 0.9954, Energy: -13.598957-0.075828j
[2025-09-03 17:18:26] [Iter 165/2250] R1[14/300], Temp: 0.9946, Energy: -14.576312-0.056763j
[2025-09-03 17:19:26] [Iter 166/2250] R1[15/300], Temp: 0.9938, Energy: -15.479513-0.083161j
[2025-09-03 17:20:26] [Iter 167/2250] R1[16/300], Temp: 0.9930, Energy: -16.426078-0.032945j
[2025-09-03 17:21:26] [Iter 168/2250] R1[17/300], Temp: 0.9921, Energy: -17.523354-0.017257j
[2025-09-03 17:22:26] [Iter 169/2250] R1[18/300], Temp: 0.9911, Energy: -18.517495-0.052137j
[2025-09-03 17:23:26] [Iter 170/2250] R1[19/300], Temp: 0.9901, Energy: -19.601996-0.106399j
[2025-09-03 17:24:27] [Iter 171/2250] R1[20/300], Temp: 0.9891, Energy: -20.624231-0.044911j
[2025-09-03 17:25:27] [Iter 172/2250] R1[21/300], Temp: 0.9880, Energy: -22.008596-0.033122j
[2025-09-03 17:26:27] [Iter 173/2250] R1[22/300], Temp: 0.9868, Energy: -23.155111+0.057599j
[2025-09-03 17:27:27] [Iter 174/2250] R1[23/300], Temp: 0.9856, Energy: -24.724582+0.111910j
[2025-09-03 17:28:27] [Iter 175/2250] R1[24/300], Temp: 0.9843, Energy: -26.477353+0.351427j
[2025-09-03 17:29:27] [Iter 176/2250] R1[25/300], Temp: 0.9830, Energy: -28.375247+0.553615j
[2025-09-03 17:30:28] [Iter 177/2250] R1[26/300], Temp: 0.9816, Energy: -30.603884+0.843468j
[2025-09-03 17:31:28] [Iter 178/2250] R1[27/300], Temp: 0.9801, Energy: -33.099957+1.097657j
[2025-09-03 17:32:28] [Iter 179/2250] R1[28/300], Temp: 0.9787, Energy: -35.946473+1.355642j
[2025-09-03 17:33:28] [Iter 180/2250] R1[29/300], Temp: 0.9771, Energy: -38.864157+1.801499j
[2025-09-03 17:34:28] [Iter 181/2250] R1[30/300], Temp: 0.9755, Energy: -42.024724+1.761264j
[2025-09-03 17:35:28] [Iter 182/2250] R1[31/300], Temp: 0.9739, Energy: -43.973905+1.486706j
[2025-09-03 17:36:29] [Iter 183/2250] R1[32/300], Temp: 0.9722, Energy: -45.406614+1.072745j
[2025-09-03 17:37:29] [Iter 184/2250] R1[33/300], Temp: 0.9704, Energy: -45.962982+0.710940j
[2025-09-03 17:38:29] [Iter 185/2250] R1[34/300], Temp: 0.9686, Energy: -45.920864+0.343047j
[2025-09-03 17:39:29] [Iter 186/2250] R1[35/300], Temp: 0.9668, Energy: -45.987147+0.185016j
[2025-09-03 17:40:29] [Iter 187/2250] R1[36/300], Temp: 0.9649, Energy: -45.946974+0.083234j
[2025-09-03 17:41:29] [Iter 188/2250] R1[37/300], Temp: 0.9629, Energy: -46.061146+0.066834j
[2025-09-03 17:42:30] [Iter 189/2250] R1[38/300], Temp: 0.9609, Energy: -46.211673+0.009485j
[2025-09-03 17:43:30] [Iter 190/2250] R1[39/300], Temp: 0.9589, Energy: -46.366591-0.009593j
[2025-09-03 17:44:30] [Iter 191/2250] R1[40/300], Temp: 0.9568, Energy: -46.522598-0.006287j
[2025-09-03 17:45:30] [Iter 192/2250] R1[41/300], Temp: 0.9546, Energy: -46.703998-0.029406j
[2025-09-03 17:46:30] [Iter 193/2250] R1[42/300], Temp: 0.9524, Energy: -46.914774-0.044445j
[2025-09-03 17:47:30] [Iter 194/2250] R1[43/300], Temp: 0.9502, Energy: -47.079353-0.035102j
[2025-09-03 17:48:30] [Iter 195/2250] R1[44/300], Temp: 0.9479, Energy: -47.283631-0.047834j
[2025-09-03 17:49:31] [Iter 196/2250] R1[45/300], Temp: 0.9455, Energy: -47.487366-0.028999j
[2025-09-03 17:50:31] [Iter 197/2250] R1[46/300], Temp: 0.9431, Energy: -47.707464-0.007083j
[2025-09-03 17:51:31] [Iter 198/2250] R1[47/300], Temp: 0.9407, Energy: -47.832983-0.017422j
[2025-09-03 17:52:31] [Iter 199/2250] R1[48/300], Temp: 0.9382, Energy: -48.024754-0.035607j
[2025-09-03 17:53:31] [Iter 200/2250] R1[49/300], Temp: 0.9356, Energy: -48.193725-0.013768j
[2025-09-03 17:54:32] [Iter 201/2250] R1[50/300], Temp: 0.9330, Energy: -48.310249-0.009023j
[2025-09-03 17:55:32] [Iter 202/2250] R1[51/300], Temp: 0.9304, Energy: -48.495206+0.017753j
[2025-09-03 17:56:32] [Iter 203/2250] R1[52/300], Temp: 0.9277, Energy: -48.572807+0.001888j
[2025-09-03 17:57:32] [Iter 204/2250] R1[53/300], Temp: 0.9249, Energy: -48.752145-0.009777j
[2025-09-03 17:58:32] [Iter 205/2250] R1[54/300], Temp: 0.9222, Energy: -48.805607+0.017601j
[2025-09-03 17:59:32] [Iter 206/2250] R1[55/300], Temp: 0.9193, Energy: -48.964047-0.014583j
[2025-09-03 18:00:33] [Iter 207/2250] R1[56/300], Temp: 0.9165, Energy: -49.094227+0.014384j
[2025-09-03 18:01:33] [Iter 208/2250] R1[57/300], Temp: 0.9135, Energy: -49.129974-0.004662j
[2025-09-03 18:02:33] [Iter 209/2250] R1[58/300], Temp: 0.9106, Energy: -49.230254+0.001518j
[2025-09-03 18:03:33] [Iter 210/2250] R1[59/300], Temp: 0.9076, Energy: -49.327446+0.009175j
[2025-09-03 18:04:33] [Iter 211/2250] R1[60/300], Temp: 0.9045, Energy: -49.442367+0.016555j
[2025-09-03 18:05:33] [Iter 212/2250] R1[61/300], Temp: 0.9014, Energy: -49.479484+0.011220j
[2025-09-03 18:06:34] [Iter 213/2250] R1[62/300], Temp: 0.8983, Energy: -49.519010+0.004469j
[2025-09-03 18:07:34] [Iter 214/2250] R1[63/300], Temp: 0.8951, Energy: -49.693226+0.019014j
[2025-09-03 18:08:34] [Iter 215/2250] R1[64/300], Temp: 0.8918, Energy: -49.710561-0.010798j
[2025-09-03 18:09:34] [Iter 216/2250] R1[65/300], Temp: 0.8886, Energy: -49.757244-0.001541j
[2025-09-03 18:10:34] [Iter 217/2250] R1[66/300], Temp: 0.8853, Energy: -49.787819+0.006962j
[2025-09-03 18:11:34] [Iter 218/2250] R1[67/300], Temp: 0.8819, Energy: -49.807007+0.038166j
[2025-09-03 18:12:35] [Iter 219/2250] R1[68/300], Temp: 0.8785, Energy: -49.879851-0.002061j
[2025-09-03 18:13:35] [Iter 220/2250] R1[69/300], Temp: 0.8751, Energy: -50.013025-0.031854j
[2025-09-03 18:14:35] [Iter 221/2250] R1[70/300], Temp: 0.8716, Energy: -50.103336+0.003588j
[2025-09-03 18:15:35] [Iter 222/2250] R1[71/300], Temp: 0.8680, Energy: -50.155931+0.008100j
[2025-09-03 18:16:35] [Iter 223/2250] R1[72/300], Temp: 0.8645, Energy: -50.165954-0.011581j
[2025-09-03 18:17:35] [Iter 224/2250] R1[73/300], Temp: 0.8609, Energy: -50.212483+0.005449j
[2025-09-03 18:18:36] [Iter 225/2250] R1[74/300], Temp: 0.8572, Energy: -50.293781+0.010008j
[2025-09-03 18:19:36] [Iter 226/2250] R1[75/300], Temp: 0.8536, Energy: -50.330723-0.016888j
[2025-09-03 18:20:36] [Iter 227/2250] R1[76/300], Temp: 0.8498, Energy: -50.355048+0.000918j
[2025-09-03 18:21:36] [Iter 228/2250] R1[77/300], Temp: 0.8461, Energy: -50.362261+0.002295j
[2025-09-03 18:22:36] [Iter 229/2250] R1[78/300], Temp: 0.8423, Energy: -50.385624-0.000363j
[2025-09-03 18:23:36] [Iter 230/2250] R1[79/300], Temp: 0.8384, Energy: -50.414765+0.008323j
[2025-09-03 18:24:37] [Iter 231/2250] R1[80/300], Temp: 0.8346, Energy: -50.458046-0.012235j
[2025-09-03 18:25:37] [Iter 232/2250] R1[81/300], Temp: 0.8307, Energy: -50.525621-0.001990j
[2025-09-03 18:26:37] [Iter 233/2250] R1[82/300], Temp: 0.8267, Energy: -50.563710+0.020782j
[2025-09-03 18:27:02] [Iter 234/2250] R1[83/300], Temp: 0.8227, Energy: -50.522520-0.015157j
[2025-09-03 18:27:23] [Iter 235/2250] R1[84/300], Temp: 0.8187, Energy: -50.534387+0.012118j
[2025-09-03 18:27:50] [Iter 236/2250] R1[85/300], Temp: 0.8147, Energy: -50.656930+0.005019j
[2025-09-03 18:28:20] [Iter 237/2250] R1[86/300], Temp: 0.8106, Energy: -50.680361-0.013369j
[2025-09-03 18:28:52] [Iter 238/2250] R1[87/300], Temp: 0.8065, Energy: -50.697039+0.006117j
[2025-09-03 18:29:52] [Iter 239/2250] R1[88/300], Temp: 0.8023, Energy: -50.718148+0.022003j
[2025-09-03 18:30:52] [Iter 240/2250] R1[89/300], Temp: 0.7981, Energy: -50.795342+0.018423j
[2025-09-03 18:31:52] [Iter 241/2250] R1[90/300], Temp: 0.7939, Energy: -50.776370-0.017690j
[2025-09-03 18:32:53] [Iter 242/2250] R1[91/300], Temp: 0.7896, Energy: -50.806812+0.005425j
[2025-09-03 18:33:53] [Iter 243/2250] R1[92/300], Temp: 0.7854, Energy: -50.775328+0.021568j
[2025-09-03 18:34:53] [Iter 244/2250] R1[93/300], Temp: 0.7810, Energy: -50.955077+0.014580j
[2025-09-03 18:35:53] [Iter 245/2250] R1[94/300], Temp: 0.7767, Energy: -50.967405+0.021499j
[2025-09-03 18:36:53] [Iter 246/2250] R1[95/300], Temp: 0.7723, Energy: -51.004673-0.014196j
[2025-09-03 18:37:53] [Iter 247/2250] R1[96/300], Temp: 0.7679, Energy: -50.986970-0.019260j
[2025-09-03 18:38:54] [Iter 248/2250] R1[97/300], Temp: 0.7635, Energy: -51.063440-0.001133j
[2025-09-03 18:39:54] [Iter 249/2250] R1[98/300], Temp: 0.7590, Energy: -51.094189+0.005050j
[2025-09-03 18:40:54] [Iter 250/2250] R1[99/300], Temp: 0.7545, Energy: -51.021693-0.002996j
[2025-09-03 18:40:54] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-03 18:41:54] [Iter 251/2250] R1[100/300], Temp: 0.7500, Energy: -51.089698+0.006333j
[2025-09-03 18:42:54] [Iter 252/2250] R1[101/300], Temp: 0.7455, Energy: -51.067662-0.012147j
[2025-09-03 18:43:55] [Iter 253/2250] R1[102/300], Temp: 0.7409, Energy: -51.164289+0.007860j
[2025-09-03 18:44:55] [Iter 254/2250] R1[103/300], Temp: 0.7363, Energy: -51.159726-0.011535j
[2025-09-03 18:45:55] [Iter 255/2250] R1[104/300], Temp: 0.7316, Energy: -51.107530+0.012153j
[2025-09-03 18:46:55] [Iter 256/2250] R1[105/300], Temp: 0.7270, Energy: -51.271445+0.001516j
[2025-09-03 18:47:55] [Iter 257/2250] R1[106/300], Temp: 0.7223, Energy: -51.286061-0.001652j
[2025-09-03 18:48:55] [Iter 258/2250] R1[107/300], Temp: 0.7176, Energy: -51.279498+0.005033j
[2025-09-03 18:49:56] [Iter 259/2250] R1[108/300], Temp: 0.7129, Energy: -51.304346+0.006313j
[2025-09-03 18:50:56] [Iter 260/2250] R1[109/300], Temp: 0.7081, Energy: -51.268098-0.011884j
[2025-09-03 18:51:56] [Iter 261/2250] R1[110/300], Temp: 0.7034, Energy: -51.270040+0.005439j
[2025-09-03 18:52:56] [Iter 262/2250] R1[111/300], Temp: 0.6986, Energy: -51.222168+0.012661j
[2025-09-03 18:53:56] [Iter 263/2250] R1[112/300], Temp: 0.6938, Energy: -51.265223+0.005477j
[2025-09-03 18:54:56] [Iter 264/2250] R1[113/300], Temp: 0.6889, Energy: -51.257751+0.024442j
[2025-09-03 18:55:57] [Iter 265/2250] R1[114/300], Temp: 0.6841, Energy: -51.283900+0.012270j
[2025-09-03 18:56:57] [Iter 266/2250] R1[115/300], Temp: 0.6792, Energy: -51.428380+0.004453j
[2025-09-03 18:57:57] [Iter 267/2250] R1[116/300], Temp: 0.6743, Energy: -51.422463+0.007668j
[2025-09-03 18:58:57] [Iter 268/2250] R1[117/300], Temp: 0.6694, Energy: -51.437585+0.002705j
[2025-09-03 18:59:57] [Iter 269/2250] R1[118/300], Temp: 0.6644, Energy: -51.439651+0.014978j
[2025-09-03 19:00:58] [Iter 270/2250] R1[119/300], Temp: 0.6595, Energy: -51.451376-0.003141j
[2025-09-03 19:01:58] [Iter 271/2250] R1[120/300], Temp: 0.6545, Energy: -51.509708-0.004253j
[2025-09-03 19:02:58] [Iter 272/2250] R1[121/300], Temp: 0.6495, Energy: -51.513939+0.005656j
[2025-09-03 19:03:58] [Iter 273/2250] R1[122/300], Temp: 0.6445, Energy: -51.474944+0.009073j
[2025-09-03 19:04:58] [Iter 274/2250] R1[123/300], Temp: 0.6395, Energy: -51.529458+0.015005j
[2025-09-03 19:05:58] [Iter 275/2250] R1[124/300], Temp: 0.6345, Energy: -51.492617-0.002034j
[2025-09-03 19:06:59] [Iter 276/2250] R1[125/300], Temp: 0.6294, Energy: -51.519819+0.007468j
[2025-09-03 19:07:59] [Iter 277/2250] R1[126/300], Temp: 0.6243, Energy: -51.508261-0.011167j
[2025-09-03 19:08:59] [Iter 278/2250] R1[127/300], Temp: 0.6193, Energy: -51.554168+0.003551j
[2025-09-03 19:09:59] [Iter 279/2250] R1[128/300], Temp: 0.6142, Energy: -51.561755-0.001373j
[2025-09-03 19:11:00] [Iter 280/2250] R1[129/300], Temp: 0.6091, Energy: -51.629010+0.001845j
[2025-09-03 19:12:00] [Iter 281/2250] R1[130/300], Temp: 0.6040, Energy: -51.599715+0.002345j
[2025-09-03 19:13:00] [Iter 282/2250] R1[131/300], Temp: 0.5988, Energy: -51.594532+0.008063j
[2025-09-03 19:14:00] [Iter 283/2250] R1[132/300], Temp: 0.5937, Energy: -51.520671+0.003697j
[2025-09-03 19:15:01] [Iter 284/2250] R1[133/300], Temp: 0.5885, Energy: -51.580976+0.003471j
[2025-09-03 19:16:01] [Iter 285/2250] R1[134/300], Temp: 0.5834, Energy: -51.555229+0.001304j
[2025-09-03 19:17:01] [Iter 286/2250] R1[135/300], Temp: 0.5782, Energy: -51.594869-0.006077j
[2025-09-03 19:18:01] [Iter 287/2250] R1[136/300], Temp: 0.5730, Energy: -51.524601-0.004826j
[2025-09-03 19:19:01] [Iter 288/2250] R1[137/300], Temp: 0.5679, Energy: -51.609994+0.001535j
[2025-09-03 19:20:01] [Iter 289/2250] R1[138/300], Temp: 0.5627, Energy: -51.657606+0.003277j
[2025-09-03 19:21:02] [Iter 290/2250] R1[139/300], Temp: 0.5575, Energy: -51.601975-0.005819j
[2025-09-03 19:22:02] [Iter 291/2250] R1[140/300], Temp: 0.5523, Energy: -51.542302+0.010138j
[2025-09-03 19:23:02] [Iter 292/2250] R1[141/300], Temp: 0.5471, Energy: -51.615526-0.006358j
[2025-09-03 19:24:02] [Iter 293/2250] R1[142/300], Temp: 0.5418, Energy: -51.667091-0.006485j
[2025-09-03 19:25:02] [Iter 294/2250] R1[143/300], Temp: 0.5366, Energy: -51.594711+0.002875j
[2025-09-03 19:26:03] [Iter 295/2250] R1[144/300], Temp: 0.5314, Energy: -51.620833-0.008358j
[2025-09-03 19:27:03] [Iter 296/2250] R1[145/300], Temp: 0.5262, Energy: -51.617448-0.009365j
[2025-09-03 19:28:03] [Iter 297/2250] R1[146/300], Temp: 0.5209, Energy: -51.602820+0.008119j
[2025-09-03 19:29:03] [Iter 298/2250] R1[147/300], Temp: 0.5157, Energy: -51.631926-0.001577j
[2025-09-03 19:30:03] [Iter 299/2250] R1[148/300], Temp: 0.5105, Energy: -51.619467-0.012864j
[2025-09-03 19:31:03] [Iter 300/2250] R1[149/300], Temp: 0.5052, Energy: -51.646549+0.007059j
[2025-09-03 19:32:03] [Iter 301/2250] R1[150/300], Temp: 0.5000, Energy: -51.619964-0.019132j
[2025-09-03 19:33:04] [Iter 302/2250] R1[151/300], Temp: 0.4948, Energy: -51.644287-0.014110j
[2025-09-03 19:34:04] [Iter 303/2250] R1[152/300], Temp: 0.4895, Energy: -51.663149-0.000885j
[2025-09-03 19:35:04] [Iter 304/2250] R1[153/300], Temp: 0.4843, Energy: -51.729582+0.005074j
[2025-09-03 19:36:04] [Iter 305/2250] R1[154/300], Temp: 0.4791, Energy: -51.690938+0.008027j
[2025-09-03 19:37:04] [Iter 306/2250] R1[155/300], Temp: 0.4738, Energy: -51.608915-0.001294j
[2025-09-03 19:38:04] [Iter 307/2250] R1[156/300], Temp: 0.4686, Energy: -51.624196-0.017506j
[2025-09-03 19:39:05] [Iter 308/2250] R1[157/300], Temp: 0.4634, Energy: -51.618848+0.006316j
[2025-09-03 19:40:05] [Iter 309/2250] R1[158/300], Temp: 0.4582, Energy: -51.678296+0.017655j
[2025-09-03 19:41:05] [Iter 310/2250] R1[159/300], Temp: 0.4529, Energy: -51.619967+0.005949j
[2025-09-03 19:42:05] [Iter 311/2250] R1[160/300], Temp: 0.4477, Energy: -51.698623-0.025747j
[2025-09-03 19:43:05] [Iter 312/2250] R1[161/300], Temp: 0.4425, Energy: -51.704952+0.010197j
[2025-09-03 19:44:06] [Iter 313/2250] R1[162/300], Temp: 0.4373, Energy: -51.708641-0.004965j
[2025-09-03 19:45:06] [Iter 314/2250] R1[163/300], Temp: 0.4321, Energy: -51.781177+0.001350j
[2025-09-03 19:46:06] [Iter 315/2250] R1[164/300], Temp: 0.4270, Energy: -51.748909+0.001073j
[2025-09-03 19:47:06] [Iter 316/2250] R1[165/300], Temp: 0.4218, Energy: -51.783288-0.001590j
[2025-09-03 19:48:06] [Iter 317/2250] R1[166/300], Temp: 0.4166, Energy: -51.700031-0.001760j
[2025-09-03 19:49:06] [Iter 318/2250] R1[167/300], Temp: 0.4115, Energy: -51.716572+0.017494j
[2025-09-03 19:50:07] [Iter 319/2250] R1[168/300], Temp: 0.4063, Energy: -51.727863+0.000186j
[2025-09-03 19:51:07] [Iter 320/2250] R1[169/300], Temp: 0.4012, Energy: -51.742815+0.002692j
[2025-09-03 19:52:07] [Iter 321/2250] R1[170/300], Temp: 0.3960, Energy: -51.746932-0.003589j
[2025-09-03 19:53:07] [Iter 322/2250] R1[171/300], Temp: 0.3909, Energy: -51.755672+0.020048j
[2025-09-03 19:54:07] [Iter 323/2250] R1[172/300], Temp: 0.3858, Energy: -51.812531+0.003800j
[2025-09-03 19:55:07] [Iter 324/2250] R1[173/300], Temp: 0.3807, Energy: -51.739259+0.001142j
[2025-09-03 19:56:08] [Iter 325/2250] R1[174/300], Temp: 0.3757, Energy: -51.744458-0.014146j
[2025-09-03 19:57:08] [Iter 326/2250] R1[175/300], Temp: 0.3706, Energy: -51.687584-0.011112j
[2025-09-03 19:58:08] [Iter 327/2250] R1[176/300], Temp: 0.3655, Energy: -51.743985+0.006234j
[2025-09-03 19:59:08] [Iter 328/2250] R1[177/300], Temp: 0.3605, Energy: -51.712556-0.001395j
[2025-09-03 20:00:08] [Iter 329/2250] R1[178/300], Temp: 0.3555, Energy: -51.789509-0.006360j
[2025-09-03 20:01:08] [Iter 330/2250] R1[179/300], Temp: 0.3505, Energy: -51.733586-0.002207j
[2025-09-03 20:02:08] [Iter 331/2250] R1[180/300], Temp: 0.3455, Energy: -51.718477-0.012207j
[2025-09-03 20:03:09] [Iter 332/2250] R1[181/300], Temp: 0.3405, Energy: -51.768759+0.002674j
[2025-09-03 20:04:09] [Iter 333/2250] R1[182/300], Temp: 0.3356, Energy: -51.786110+0.015219j
[2025-09-03 20:05:09] [Iter 334/2250] R1[183/300], Temp: 0.3306, Energy: -51.730113-0.010972j
[2025-09-03 20:06:09] [Iter 335/2250] R1[184/300], Temp: 0.3257, Energy: -51.770665+0.006259j
[2025-09-03 20:07:09] [Iter 336/2250] R1[185/300], Temp: 0.3208, Energy: -51.774411+0.001804j
[2025-09-03 20:08:09] [Iter 337/2250] R1[186/300], Temp: 0.3159, Energy: -51.778178-0.005725j
[2025-09-03 20:09:10] [Iter 338/2250] R1[187/300], Temp: 0.3111, Energy: -51.731447-0.000083j
[2025-09-03 20:10:10] [Iter 339/2250] R1[188/300], Temp: 0.3062, Energy: -51.810819+0.007947j
[2025-09-03 20:11:10] [Iter 340/2250] R1[189/300], Temp: 0.3014, Energy: -51.818272-0.003909j
[2025-09-03 20:12:10] [Iter 341/2250] R1[190/300], Temp: 0.2966, Energy: -51.841594-0.013392j
[2025-09-03 20:13:10] [Iter 342/2250] R1[191/300], Temp: 0.2919, Energy: -51.793610+0.003693j
[2025-09-03 20:14:10] [Iter 343/2250] R1[192/300], Temp: 0.2871, Energy: -51.824865-0.001119j
[2025-09-03 20:15:11] [Iter 344/2250] R1[193/300], Temp: 0.2824, Energy: -51.769849+0.004365j
[2025-09-03 20:16:11] [Iter 345/2250] R1[194/300], Temp: 0.2777, Energy: -51.793276-0.006120j
[2025-09-03 20:17:11] [Iter 346/2250] R1[195/300], Temp: 0.2730, Energy: -51.832113+0.003715j
[2025-09-03 20:18:11] [Iter 347/2250] R1[196/300], Temp: 0.2684, Energy: -51.885878+0.010918j
[2025-09-03 20:19:11] [Iter 348/2250] R1[197/300], Temp: 0.2637, Energy: -51.896466+0.005460j
[2025-09-03 20:20:11] [Iter 349/2250] R1[198/300], Temp: 0.2591, Energy: -51.835808-0.015077j
[2025-09-03 20:21:12] [Iter 350/2250] R1[199/300], Temp: 0.2545, Energy: -51.822099-0.001850j
[2025-09-03 20:22:12] [Iter 351/2250] R1[200/300], Temp: 0.2500, Energy: -51.878956+0.003583j
[2025-09-03 20:23:12] [Iter 352/2250] R1[201/300], Temp: 0.2455, Energy: -51.853944-0.012076j
[2025-09-03 20:24:12] [Iter 353/2250] R1[202/300], Temp: 0.2410, Energy: -51.876230-0.003305j
[2025-09-03 20:25:12] [Iter 354/2250] R1[203/300], Temp: 0.2365, Energy: -51.875757-0.000285j
[2025-09-03 20:26:12] [Iter 355/2250] R1[204/300], Temp: 0.2321, Energy: -51.757199-0.002515j
[2025-09-03 20:27:13] [Iter 356/2250] R1[205/300], Temp: 0.2277, Energy: -51.825028+0.006428j
[2025-09-03 20:28:13] [Iter 357/2250] R1[206/300], Temp: 0.2233, Energy: -51.761355-0.009503j
[2025-09-03 20:29:13] [Iter 358/2250] R1[207/300], Temp: 0.2190, Energy: -51.811085-0.007890j
[2025-09-03 20:30:13] [Iter 359/2250] R1[208/300], Temp: 0.2146, Energy: -51.792614-0.000729j
[2025-09-03 20:31:13] [Iter 360/2250] R1[209/300], Temp: 0.2104, Energy: -51.845274-0.002408j
[2025-09-03 20:32:13] [Iter 361/2250] R1[210/300], Temp: 0.2061, Energy: -51.821572+0.004966j
[2025-09-03 20:33:14] [Iter 362/2250] R1[211/300], Temp: 0.2019, Energy: -51.904375+0.000673j
[2025-09-03 20:34:14] [Iter 363/2250] R1[212/300], Temp: 0.1977, Energy: -51.841119+0.003774j
[2025-09-03 20:35:14] [Iter 364/2250] R1[213/300], Temp: 0.1935, Energy: -51.885177+0.011180j
[2025-09-03 20:36:14] [Iter 365/2250] R1[214/300], Temp: 0.1894, Energy: -51.887020-0.001303j
[2025-09-03 20:37:14] [Iter 366/2250] R1[215/300], Temp: 0.1853, Energy: -51.866318+0.006542j
[2025-09-03 20:38:14] [Iter 367/2250] R1[216/300], Temp: 0.1813, Energy: -51.846365+0.006315j
[2025-09-03 20:39:15] [Iter 368/2250] R1[217/300], Temp: 0.1773, Energy: -51.846221+0.003156j
[2025-09-03 20:40:15] [Iter 369/2250] R1[218/300], Temp: 0.1733, Energy: -51.881756+0.004308j
[2025-09-03 20:41:15] [Iter 370/2250] R1[219/300], Temp: 0.1693, Energy: -51.864232-0.009121j
[2025-09-03 20:42:15] [Iter 371/2250] R1[220/300], Temp: 0.1654, Energy: -51.863417-0.008342j
[2025-09-03 20:43:15] [Iter 372/2250] R1[221/300], Temp: 0.1616, Energy: -51.925250-0.010930j
[2025-09-03 20:44:15] [Iter 373/2250] R1[222/300], Temp: 0.1577, Energy: -51.872946+0.002592j
[2025-09-03 20:45:16] [Iter 374/2250] R1[223/300], Temp: 0.1539, Energy: -51.827940-0.013789j
[2025-09-03 20:46:16] [Iter 375/2250] R1[224/300], Temp: 0.1502, Energy: -51.848798-0.000531j
[2025-09-03 20:47:16] [Iter 376/2250] R1[225/300], Temp: 0.1464, Energy: -51.884356-0.011176j
[2025-09-03 20:48:16] [Iter 377/2250] R1[226/300], Temp: 0.1428, Energy: -51.838056+0.000001j
[2025-09-03 20:49:16] [Iter 378/2250] R1[227/300], Temp: 0.1391, Energy: -51.861779-0.010519j
[2025-09-03 20:50:16] [Iter 379/2250] R1[228/300], Temp: 0.1355, Energy: -51.884259-0.007043j
[2025-09-03 20:51:16] [Iter 380/2250] R1[229/300], Temp: 0.1320, Energy: -51.836103-0.004644j
[2025-09-03 20:52:17] [Iter 381/2250] R1[230/300], Temp: 0.1284, Energy: -51.875753+0.004337j
[2025-09-03 20:53:17] [Iter 382/2250] R1[231/300], Temp: 0.1249, Energy: -51.846247-0.004793j
[2025-09-03 20:54:17] [Iter 383/2250] R1[232/300], Temp: 0.1215, Energy: -51.905266+0.006962j
[2025-09-03 20:55:17] [Iter 384/2250] R1[233/300], Temp: 0.1181, Energy: -51.760865-0.000181j
[2025-09-03 20:56:17] [Iter 385/2250] R1[234/300], Temp: 0.1147, Energy: -51.846373-0.009895j
[2025-09-03 20:57:17] [Iter 386/2250] R1[235/300], Temp: 0.1114, Energy: -51.848160-0.006412j
[2025-09-03 20:58:18] [Iter 387/2250] R1[236/300], Temp: 0.1082, Energy: -51.808503-0.003215j
[2025-09-03 20:59:18] [Iter 388/2250] R1[237/300], Temp: 0.1049, Energy: -51.831231+0.004719j
[2025-09-03 21:00:18] [Iter 389/2250] R1[238/300], Temp: 0.1017, Energy: -51.856725+0.003747j
[2025-09-03 21:01:18] [Iter 390/2250] R1[239/300], Temp: 0.0986, Energy: -51.851808+0.009422j
[2025-09-03 21:02:18] [Iter 391/2250] R1[240/300], Temp: 0.0955, Energy: -51.852755+0.001577j
[2025-09-03 21:03:18] [Iter 392/2250] R1[241/300], Temp: 0.0924, Energy: -51.938107-0.000157j
[2025-09-03 21:04:19] [Iter 393/2250] R1[242/300], Temp: 0.0894, Energy: -51.875358+0.000917j
[2025-09-03 21:05:19] [Iter 394/2250] R1[243/300], Temp: 0.0865, Energy: -51.896991-0.000597j
[2025-09-03 21:06:19] [Iter 395/2250] R1[244/300], Temp: 0.0835, Energy: -51.861239-0.005825j
[2025-09-03 21:07:19] [Iter 396/2250] R1[245/300], Temp: 0.0807, Energy: -51.852970-0.000493j
[2025-09-03 21:08:19] [Iter 397/2250] R1[246/300], Temp: 0.0778, Energy: -51.855663+0.005087j
[2025-09-03 21:09:19] [Iter 398/2250] R1[247/300], Temp: 0.0751, Energy: -51.889887+0.003328j
[2025-09-03 21:10:20] [Iter 399/2250] R1[248/300], Temp: 0.0723, Energy: -51.855840-0.004440j
[2025-09-03 21:11:20] [Iter 400/2250] R1[249/300], Temp: 0.0696, Energy: -51.930675-0.006747j
[2025-09-03 21:12:20] [Iter 401/2250] R1[250/300], Temp: 0.0670, Energy: -51.939075+0.004591j
[2025-09-03 21:13:20] [Iter 402/2250] R1[251/300], Temp: 0.0644, Energy: -51.938057+0.000703j
[2025-09-03 21:14:20] [Iter 403/2250] R1[252/300], Temp: 0.0618, Energy: -52.021176+0.002094j
[2025-09-03 21:15:20] [Iter 404/2250] R1[253/300], Temp: 0.0593, Energy: -52.013578-0.009070j
[2025-09-03 21:16:21] [Iter 405/2250] R1[254/300], Temp: 0.0569, Energy: -51.927605+0.004025j
[2025-09-03 21:17:21] [Iter 406/2250] R1[255/300], Temp: 0.0545, Energy: -51.926324+0.002968j
[2025-09-03 21:18:21] [Iter 407/2250] R1[256/300], Temp: 0.0521, Energy: -51.920965-0.011976j
[2025-09-03 21:19:21] [Iter 408/2250] R1[257/300], Temp: 0.0498, Energy: -51.955533+0.003902j
[2025-09-03 21:20:21] [Iter 409/2250] R1[258/300], Temp: 0.0476, Energy: -51.841745-0.000800j
[2025-09-03 21:21:21] [Iter 410/2250] R1[259/300], Temp: 0.0454, Energy: -51.895162+0.003301j
[2025-09-03 21:22:22] [Iter 411/2250] R1[260/300], Temp: 0.0432, Energy: -51.934410-0.001796j
[2025-09-03 21:23:22] [Iter 412/2250] R1[261/300], Temp: 0.0411, Energy: -51.988135-0.007080j
[2025-09-03 21:24:22] [Iter 413/2250] R1[262/300], Temp: 0.0391, Energy: -51.974937+0.004867j
[2025-09-03 21:25:22] [Iter 414/2250] R1[263/300], Temp: 0.0371, Energy: -51.953831-0.001400j
[2025-09-03 21:26:22] [Iter 415/2250] R1[264/300], Temp: 0.0351, Energy: -51.916337-0.003754j
[2025-09-03 21:27:22] [Iter 416/2250] R1[265/300], Temp: 0.0332, Energy: -51.905420-0.004500j
[2025-09-03 21:28:22] [Iter 417/2250] R1[266/300], Temp: 0.0314, Energy: -51.940570+0.004600j
[2025-09-03 21:29:23] [Iter 418/2250] R1[267/300], Temp: 0.0296, Energy: -51.950305+0.008017j
[2025-09-03 21:30:23] [Iter 419/2250] R1[268/300], Temp: 0.0278, Energy: -51.960451+0.002940j
[2025-09-03 21:31:23] [Iter 420/2250] R1[269/300], Temp: 0.0261, Energy: -51.975402+0.004876j
[2025-09-03 21:32:23] [Iter 421/2250] R1[270/300], Temp: 0.0245, Energy: -52.020200-0.005234j
[2025-09-03 21:33:23] [Iter 422/2250] R1[271/300], Temp: 0.0229, Energy: -51.970924-0.000658j
[2025-09-03 21:34:23] [Iter 423/2250] R1[272/300], Temp: 0.0213, Energy: -51.923576+0.001323j
[2025-09-03 21:35:23] [Iter 424/2250] R1[273/300], Temp: 0.0199, Energy: -52.021251+0.002770j
[2025-09-03 21:36:24] [Iter 425/2250] R1[274/300], Temp: 0.0184, Energy: -51.923605+0.007302j
[2025-09-03 21:37:24] [Iter 426/2250] R1[275/300], Temp: 0.0170, Energy: -51.975810+0.005911j
[2025-09-03 21:38:24] [Iter 427/2250] R1[276/300], Temp: 0.0157, Energy: -51.953246-0.000794j
[2025-09-03 21:39:24] [Iter 428/2250] R1[277/300], Temp: 0.0144, Energy: -51.938218-0.000912j
[2025-09-03 21:40:24] [Iter 429/2250] R1[278/300], Temp: 0.0132, Energy: -51.892691-0.001236j
[2025-09-03 21:41:24] [Iter 430/2250] R1[279/300], Temp: 0.0120, Energy: -51.922647+0.007099j
[2025-09-03 21:42:25] [Iter 431/2250] R1[280/300], Temp: 0.0109, Energy: -51.969297+0.005032j
[2025-09-03 21:43:25] [Iter 432/2250] R1[281/300], Temp: 0.0099, Energy: -51.883961+0.002647j
[2025-09-03 21:44:25] [Iter 433/2250] R1[282/300], Temp: 0.0089, Energy: -51.940939-0.001922j
[2025-09-03 21:45:25] [Iter 434/2250] R1[283/300], Temp: 0.0079, Energy: -51.978605+0.002614j
[2025-09-03 21:46:25] [Iter 435/2250] R1[284/300], Temp: 0.0070, Energy: -51.972913-0.004074j
[2025-09-03 21:47:25] [Iter 436/2250] R1[285/300], Temp: 0.0062, Energy: -51.978948+0.004442j
[2025-09-03 21:48:26] [Iter 437/2250] R1[286/300], Temp: 0.0054, Energy: -51.971277-0.002101j
[2025-09-03 21:49:26] [Iter 438/2250] R1[287/300], Temp: 0.0046, Energy: -52.043664-0.005738j
[2025-09-03 21:50:26] [Iter 439/2250] R1[288/300], Temp: 0.0039, Energy: -52.033626-0.004589j
[2025-09-03 21:51:26] [Iter 440/2250] R1[289/300], Temp: 0.0033, Energy: -52.032104+0.001519j
[2025-09-03 21:52:26] [Iter 441/2250] R1[290/300], Temp: 0.0027, Energy: -52.041380-0.005252j
[2025-09-03 21:53:26] [Iter 442/2250] R1[291/300], Temp: 0.0022, Energy: -52.045064+0.005850j
[2025-09-03 21:54:27] [Iter 443/2250] R1[292/300], Temp: 0.0018, Energy: -52.017773+0.004999j
[2025-09-03 21:55:27] [Iter 444/2250] R1[293/300], Temp: 0.0013, Energy: -51.980879-0.001145j
[2025-09-03 21:56:27] [Iter 445/2250] R1[294/300], Temp: 0.0010, Energy: -51.934318+0.001989j
[2025-09-03 21:57:27] [Iter 446/2250] R1[295/300], Temp: 0.0007, Energy: -52.000354+0.006764j
[2025-09-03 21:58:27] [Iter 447/2250] R1[296/300], Temp: 0.0004, Energy: -51.947669-0.002575j
[2025-09-03 21:59:27] [Iter 448/2250] R1[297/300], Temp: 0.0002, Energy: -51.887542-0.003149j
[2025-09-03 22:00:27] [Iter 449/2250] R1[298/300], Temp: 0.0001, Energy: -51.947403+0.001001j
[2025-09-03 22:01:27] [Iter 450/2250] R1[299/300], Temp: 0.0000, Energy: -51.937933-0.004536j
[2025-09-03 22:01:27] RESTART #2 | Period: 600
[2025-09-03 22:02:28] [Iter 451/2250] R2[0/600], Temp: 1.0000, Energy: -51.913960-0.006579j
[2025-09-03 22:03:28] [Iter 452/2250] R2[1/600], Temp: 1.0000, Energy: -51.992535-0.005602j
[2025-09-03 22:04:28] [Iter 453/2250] R2[2/600], Temp: 1.0000, Energy: -51.941365+0.003386j
[2025-09-03 22:05:28] [Iter 454/2250] R2[3/600], Temp: 0.9999, Energy: -51.939879+0.001693j
[2025-09-03 22:06:28] [Iter 455/2250] R2[4/600], Temp: 0.9999, Energy: -51.975366+0.001538j
[2025-09-03 22:07:28] [Iter 456/2250] R2[5/600], Temp: 0.9998, Energy: -51.958140-0.002709j
[2025-09-03 22:08:28] [Iter 457/2250] R2[6/600], Temp: 0.9998, Energy: -51.975717-0.002509j
[2025-09-03 22:09:29] [Iter 458/2250] R2[7/600], Temp: 0.9997, Energy: -52.020806-0.000017j
[2025-09-03 22:10:29] [Iter 459/2250] R2[8/600], Temp: 0.9996, Energy: -51.998487+0.000966j
[2025-09-03 22:11:29] [Iter 460/2250] R2[9/600], Temp: 0.9994, Energy: -52.043649+0.004177j
[2025-09-03 22:12:29] [Iter 461/2250] R2[10/600], Temp: 0.9993, Energy: -52.022795-0.002608j
[2025-09-03 22:13:29] [Iter 462/2250] R2[11/600], Temp: 0.9992, Energy: -52.012582-0.003541j
[2025-09-03 22:14:29] [Iter 463/2250] R2[12/600], Temp: 0.9990, Energy: -52.029876-0.001648j
[2025-09-03 22:15:30] [Iter 464/2250] R2[13/600], Temp: 0.9988, Energy: -51.972884-0.004311j
[2025-09-03 22:16:30] [Iter 465/2250] R2[14/600], Temp: 0.9987, Energy: -51.993126+0.003000j
[2025-09-03 22:17:30] [Iter 466/2250] R2[15/600], Temp: 0.9985, Energy: -52.014586-0.000394j
[2025-09-03 22:18:28] [Iter 467/2250] R2[16/600], Temp: 0.9982, Energy: -51.992204-0.001373j
[2025-09-03 22:19:08] [Iter 468/2250] R2[17/600], Temp: 0.9980, Energy: -51.951299-0.000996j
[2025-09-03 22:19:48] [Iter 469/2250] R2[18/600], Temp: 0.9978, Energy: -51.895501-0.001083j
[2025-09-03 22:20:08] [Iter 470/2250] R2[19/600], Temp: 0.9975, Energy: -51.948621-0.003878j
[2025-09-03 22:20:28] [Iter 471/2250] R2[20/600], Temp: 0.9973, Energy: -51.941181+0.003346j
[2025-09-03 22:20:56] [Iter 472/2250] R2[21/600], Temp: 0.9970, Energy: -51.993338+0.003252j
[2025-09-03 22:21:26] [Iter 473/2250] R2[22/600], Temp: 0.9967, Energy: -51.981858-0.000510j
[2025-09-03 22:22:00] [Iter 474/2250] R2[23/600], Temp: 0.9964, Energy: -51.971356+0.002374j
[2025-09-03 22:23:00] [Iter 475/2250] R2[24/600], Temp: 0.9961, Energy: -51.945356+0.002241j
[2025-09-03 22:24:00] [Iter 476/2250] R2[25/600], Temp: 0.9957, Energy: -51.967487+0.003003j
[2025-09-03 22:25:00] [Iter 477/2250] R2[26/600], Temp: 0.9954, Energy: -51.959955+0.000593j
[2025-09-03 22:26:00] [Iter 478/2250] R2[27/600], Temp: 0.9950, Energy: -51.991750-0.000342j
[2025-09-03 22:27:00] [Iter 479/2250] R2[28/600], Temp: 0.9946, Energy: -52.016816-0.007847j
[2025-09-03 22:28:00] [Iter 480/2250] R2[29/600], Temp: 0.9942, Energy: -51.957880-0.000032j
[2025-09-03 22:29:01] [Iter 481/2250] R2[30/600], Temp: 0.9938, Energy: -51.988509-0.007968j
[2025-09-03 22:30:01] [Iter 482/2250] R2[31/600], Temp: 0.9934, Energy: -52.009126-0.003270j
[2025-09-03 22:31:01] [Iter 483/2250] R2[32/600], Temp: 0.9930, Energy: -51.988550+0.000997j
[2025-09-03 22:32:01] [Iter 484/2250] R2[33/600], Temp: 0.9926, Energy: -51.934904-0.000592j
[2025-09-03 22:33:02] [Iter 485/2250] R2[34/600], Temp: 0.9921, Energy: -51.948392-0.000997j
[2025-09-03 22:34:02] [Iter 486/2250] R2[35/600], Temp: 0.9916, Energy: -51.949291+0.000581j
[2025-09-03 22:35:02] [Iter 487/2250] R2[36/600], Temp: 0.9911, Energy: -52.019539+0.004883j
[2025-09-03 22:36:03] [Iter 488/2250] R2[37/600], Temp: 0.9906, Energy: -52.030417+0.001813j
[2025-09-03 22:37:03] [Iter 489/2250] R2[38/600], Temp: 0.9901, Energy: -52.034695+0.004134j
[2025-09-03 22:38:03] [Iter 490/2250] R2[39/600], Temp: 0.9896, Energy: -52.029426+0.002223j
[2025-09-03 22:39:03] [Iter 491/2250] R2[40/600], Temp: 0.9891, Energy: -52.042421+0.002809j
[2025-09-03 22:40:04] [Iter 492/2250] R2[41/600], Temp: 0.9885, Energy: -52.029685-0.002798j
[2025-09-03 22:41:04] [Iter 493/2250] R2[42/600], Temp: 0.9880, Energy: -51.930491+0.006689j
[2025-09-03 22:42:04] [Iter 494/2250] R2[43/600], Temp: 0.9874, Energy: -51.983395+0.001610j
[2025-09-03 22:43:04] [Iter 495/2250] R2[44/600], Temp: 0.9868, Energy: -52.037002-0.000937j
[2025-09-03 22:44:05] [Iter 496/2250] R2[45/600], Temp: 0.9862, Energy: -52.002007-0.000521j
[2025-09-03 22:45:05] [Iter 497/2250] R2[46/600], Temp: 0.9856, Energy: -52.018433-0.000066j
[2025-09-03 22:46:05] [Iter 498/2250] R2[47/600], Temp: 0.9849, Energy: -52.058615+0.003974j
[2025-09-03 22:47:05] [Iter 499/2250] R2[48/600], Temp: 0.9843, Energy: -52.078546-0.004911j
[2025-09-03 22:48:05] [Iter 500/2250] R2[49/600], Temp: 0.9836, Energy: -52.049877-0.000819j
[2025-09-03 22:48:05] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-09-03 22:49:05] [Iter 501/2250] R2[50/600], Temp: 0.9830, Energy: -52.021842-0.001853j
[2025-09-03 22:50:06] [Iter 502/2250] R2[51/600], Temp: 0.9823, Energy: -52.093152-0.001614j
[2025-09-03 22:51:06] [Iter 503/2250] R2[52/600], Temp: 0.9816, Energy: -52.014183+0.002792j
[2025-09-03 22:52:06] [Iter 504/2250] R2[53/600], Temp: 0.9809, Energy: -51.991518+0.000543j
[2025-09-03 22:53:06] [Iter 505/2250] R2[54/600], Temp: 0.9801, Energy: -52.033638-0.001680j
[2025-09-03 22:54:06] [Iter 506/2250] R2[55/600], Temp: 0.9794, Energy: -52.005461-0.000922j
[2025-09-03 22:55:07] [Iter 507/2250] R2[56/600], Temp: 0.9787, Energy: -51.976856-0.000094j
[2025-09-03 22:56:07] [Iter 508/2250] R2[57/600], Temp: 0.9779, Energy: -52.053794+0.005119j
[2025-09-03 22:57:07] [Iter 509/2250] R2[58/600], Temp: 0.9771, Energy: -51.943946-0.000988j
[2025-09-03 22:58:07] [Iter 510/2250] R2[59/600], Temp: 0.9763, Energy: -51.945967+0.001500j
[2025-09-03 22:59:08] [Iter 511/2250] R2[60/600], Temp: 0.9755, Energy: -51.985028+0.000502j
[2025-09-03 23:00:08] [Iter 512/2250] R2[61/600], Temp: 0.9747, Energy: -51.986704-0.004420j
[2025-09-03 23:01:08] [Iter 513/2250] R2[62/600], Temp: 0.9739, Energy: -51.998144-0.000873j
[2025-09-03 23:02:08] [Iter 514/2250] R2[63/600], Temp: 0.9730, Energy: -52.003820+0.002765j
